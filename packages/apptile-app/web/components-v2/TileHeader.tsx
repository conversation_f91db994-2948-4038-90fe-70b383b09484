import React from 'react';
import {StyleSheet} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {selectSelectedPluginConfig} from '@/root/web/selectors/EditorSelectors';
import {sendTileAnalytics} from '../actions/editorActions';
import {ApptileWebIcon} from '../icons/ApptileWebIcon';
import {Icon} from 'apptile-core';
import Analytics from '@/root/web/lib/segment';
import theme from '../views/prompt-to-app/styles-prompt-to-app/theme';

const styles = StyleSheet.create({
  header: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  tile: {
    fontSize: 12,
    marginBottom: 4,
    color: '#B0B0B0',
  },
  headerText: {
    fontWeight: '500',
    opacity: 0.8,
    color: '#ffffff',
    fontSize: 18,
  },
  tileName: {
    fontWeight: '600',
    wordBreak: 'break-word',
    width: '100%',
    color: theme.FOREGROUND,
  },
  delContainer: {
    width: '20%',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  delIcon: {
    // width: 36,
    // height: 36,
    padding: 5,
    // backgroundColor: '#fce5e6',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    // borderColor: '#D80707',
    // borderWidth: 1,
  },
  codeButton: {
    backgroundColor: '#000000',
    padding: 10,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    color: '#007AFF',
    fontFamily: 'Inter',
    fontSize: 13,
    fontWeight: '500',
    cursor: 'pointer',
    letterSpacing: -0.5,
  },
});

const TileHeader = (props: any) => {
  const {isDeletable, onDelete, onChat, onEditCode} = props;
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const dispatch = useDispatch();
  if (!moduleConfig) return null;
  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  let title = moduleConfig?.config?.get('moduleName') ?? '';
  let isSDKPlugin = false;
  if (!title && moduleConfig.get('type') === 'widget') {
    title = moduleConfig.get('subtype');
    isSDKPlugin = true;
  }

  return (
    <div style={{marginBottom: 20}}>
      <div style={styles.header}>
        <span style={styles.headerText}>Tile Properties</span>
        <div style={styles.codeButton} onClick={() => {
          Analytics.track('code_editor_opened');
          onEditCode(title)
          }}>
          {'</> Code'}
        </div>
        {/* <div style={{display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
          {!onEditCode ? (
            <></>
          ) : (
            <Pressable onPress={() => onEditCode(title)} style={{padding: 5}}>
              <Icon name="code" iconType="MaterialIcons" color="#ffffff" size={20} />
            </Pressable>
          )}
          <Pressable onPress={onChat} style={{padding: 5}}>
            <Icon name={'chat'} iconType={'MaterialIcons'} color="#0062ff" size={20} />
          </Pressable>
          {isDeletable && (
            <View style={styles.delIcon}>
              <ApptileWebIcon
                name={'delete'}
                color="#D80707"
                size={20}
                onPress={() => {
                  dispatch(sendTileAnalytics(moduleUUID, 'editor:tile_deleted', {}));
                  onDelete();
                }}
              />
            </View>
          )}
        </div> */}
      </div>
    </div>
  );
};

export default TileHeader;
