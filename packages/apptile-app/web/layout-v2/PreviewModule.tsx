import {useSelector, useDispatch} from 'react-redux';
import Button from '../components-v2/base/Button';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {generateMobilePreview, generateWebPreview} from '../../../apptile-core/actions/DispatchActions';
import {resetMobilePreviewStatus} from '../actions/editorActions';
import {useState, useEffect, useRef} from 'react';
import QRCode from 'react-qr-code';
import {Text} from 'react-native';
import theme from '../views/prompt-to-app/styles-prompt-to-app/theme';
import {Icon} from 'apptile-core';
import GradientLoader from '../components/GradientLoader';
import Analytics from '@/root/web/lib/segment';

const WebPreview = ({appId, isGeneratingWebPreview}: {appId: string; isGeneratingWebPreview: boolean}) => {
  const dispatch = useDispatch();
  const webPreviewLink = useSelector((state: EditorRootState) => state.editor.webPreviewLink);
  // const generateWebPreviewError = useSelector((state: EditorRootState) => state.editor.generateWebPreviewError);
  const [copying, setCopying] = useState(false);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(webPreviewLink || '');
    setCopying(true);

    let timeout = setTimeout(() => {
      setCopying(false);

      clearTimeout(timeout);
    }, 3000);
    Analytics.track('web_preview_shared');
  };

  useEffect(() => {
    dispatch(generateWebPreview(appId));
  }, []);

  return (
    <>
      <Text style={styles.textInsideModal}>Preview weblink</Text>
      <div
        style={{
          backgroundColor: '#272F38',
          padding: 16,
          borderRadius: 16,
          marginTop: 16,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <div style={{color: '#E0EFFF', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}}>
          {isGeneratingWebPreview ? 'Generating...' : webPreviewLink || ''}
        </div>

        <div
          onClick={isGeneratingWebPreview ? undefined : handleCopyLink}
          style={{
            transform: isGeneratingWebPreview ? undefined : copying ? undefined : 'rotateY(180deg)',
            cursor: isGeneratingWebPreview ? 'not-allowed' : 'pointer',
            marginLeft: 16,
            borderRadius: 4,
            backgroundColor: isGeneratingWebPreview ? undefined : copying ? 'green' : undefined,
          }}>
          {isGeneratingWebPreview ? (
            <GradientLoader size={20} backgroundColor="#272F38" thickness={2} />
          ) : (
            <Icon
              name={copying ? 'check' : 'content-copy'}
              size={20}
              color={copying ? 'white' : '#979797'}
              iconType="MaterialCommunityIcons"
            />
          )}
        </div>
      </div>
    </>
  );
};

const MobilePreview = ({appId, isGeneratingMobilePreview}: {appId: string; isGeneratingMobilePreview: boolean}) => {
  const dispatch = useDispatch();
  const apptile = useSelector((state: EditorRootState) => state.apptile);
  const mobilePreviewStatus = useSelector((state: EditorRootState) => state.editor.mobilePreviewStatus);

  // Handle mobile preview generation
  const handleGenerateMobilePreview = () => {
    dispatch(generateMobilePreview(appId, apptile.forkId?.toString()!, apptile.appBranch!));
  };

  // Render popover content based on current step
  const renderPopoverContent = () => {
    if (isGeneratingMobilePreview) {
      return <GradientLoader size={40} backgroundColor="#121212" />;
    }
    switch (mobilePreviewStatus) {
      case 'idle':
        return (
          <span
            style={{color: '#007AFF', fontSize: 14, textDecorationLine: 'underline', cursor: 'pointer'}}
            onClick={handleGenerateMobilePreview}>
            Generate QR Code
          </span>
        );
      case 'loading':
        return <GradientLoader size={40} backgroundColor="#121212" />;
      case 'success':
        return <QRCode value={`APP_ID=${appId}`} size={128} bgColor="transparent" fgColor="white" level="L" />;
      case 'error':
        return (
          <span
            style={{color: '#007AFF', fontSize: 14, textDecorationLine: 'underline', cursor: 'pointer'}}
            onClick={handleGenerateMobilePreview}>
            Try Again
          </span>
        );
    }
  };

  return (
    <div style={{marginTop: 16 * 2}}>
      <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start'}}>
        <Text style={styles.textInsideModal}>Preview on Mobile</Text>
        {renderPopoverContent()}
      </div>
    </div>
  );
};

const PreviewModule = ({appId}: {appId: string}) => {
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const isGeneratingWebPreview = useSelector((state: EditorRootState) => state.editor.isGeneratingWebPreview);
  const isGeneratingMobilePreview = useSelector((state: EditorRootState) => state.editor.isGeneratingMobilePreview);

  const loading = isGeneratingWebPreview || isGeneratingMobilePreview;

  return (
    <div style={{display: 'inline'}}>
      <Button
        disabled={loading || previewModalOpen}
        variant="FILLED"
        color="DEFAULT"
        containerStyles={{
          ...(previewModalOpen ? styles.generatePreviewButtonHighlight : styles.generatePreviewButton),
          ...(loading || previewModalOpen ? {pointerEvents: 'none'} : {}),
        }}
        onPress={() => {
          setPreviewModalOpen(true);
        }}>
        Generate a Live Preview
      </Button>
      {previewModalOpen ? (
        <div
          style={{
            backgroundColor: '#121212',
            width: 400,
            padding: 20,
            borderRadius: 20,
            marginTop: 16,
            position: 'fixed',
            zIndex: 2147483647,
          }}>
          <WebPreview appId={appId} isGeneratingWebPreview={isGeneratingWebPreview} />
          <MobilePreview appId={appId} isGeneratingMobilePreview={isGeneratingMobilePreview} />
          {loading ? (
            <></>
          ) : (
            <div
              style={{
                position: 'absolute',
                top: 16,
                right: 16,
                cursor: 'pointer',
              }}
              onClick={() => setPreviewModalOpen(false)}>
              <Icon name="close" size={16} color="#A4B1BF" iconType="MaterialCommunityIcons" />
            </div>
          )}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
};

const styles = {
  generatePreviewButton: {
    width: 250,
    borderRadius: 8,
    backgroundColor: '#182232',
    borderColor: '#182232',
    borderWidth: 3,
  },
  generatePreviewButtonHighlight: {
    width: 250,
    borderRadius: 8,
    backgroundColor: '#182232',
    borderColor: '#007AFF',
    borderWidth: 3,
  },
  textInsideModal: {
    color: '#E0EFFF',
    fontSize: 18,
  },
  popoverContentDiv: {
    backgroundColor: theme.FOREGROUND,
    width: 200,
    marginTop: 10,
    border: '1px solid black',
    borderRadius: 5,
    padding: '16px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  } as React.CSSProperties,
};

export default PreviewModule;
