import React, {useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {GoogleAuthConfig} from '../../config/googleAuth';
import {loginWithGoogle} from '../../actions/editorActions';
import Analytics from '@/root/web/lib/segment';

declare global {
  interface Window {
    google: any;
  }
}

interface GoogleSignInButtonProps {
  onLoginStart?: () => void;
}

export const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({onLoginStart}) => {
  const dispatch = useDispatch();

  useEffect(() => {
    const handleCredentialResponse = (response: any) => {
      Analytics.track('google_sign_in_button_clicked');
      // Notify parent component that login has started
      if (onLoginStart) {
        onLoginStart();
      }

      // response.credential contains the JWT token from Google
      dispatch(loginWithGoogle(response.credential));
    };

    const initializeGoogleAuth = () => {
      window.google.accounts.id.initialize({
        client_id: GoogleAuthConfig.CLIENT_ID,
        callback: handleCredentialResponse,
      });

      // Enhanced button styling while maintaining Google's brand identity
      window.google.accounts.id.renderButton(document.getElementById('googleSignInDiv'), {
        type: 'standard',
        theme: 'outline',
        size: 'large',
        text: 'continue_with',
        shape: 'rectangular',
        logo_alignment: 'left',
        width: 320,
      });
    };

    if (window.google?.accounts?.id) {
      initializeGoogleAuth();
    } else {
      const script = document.querySelector('script[src="https://accounts.google.com/gsi/client"]');
      script?.addEventListener('load', initializeGoogleAuth);
    }
  }, [dispatch, onLoginStart]);

  return (
    <div className="google-sign-in-container">
      <div id="googleSignInDiv" className="google-sign-in-button" />
      <style jsx>{`
        .google-sign-in-container {
          width: 100%;
          display: flex;
          justify-content: center;
          margin: 12px 0;
        }
        .google-sign-in-button {
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
        .google-sign-in-button:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          transform: translateY(-1px);
        }
      `}</style>
    </div>
  );
};
