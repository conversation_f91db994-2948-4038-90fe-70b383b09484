import React, {ReactElement, useState, forwardRef, useImperativeHandle, useRef} from 'react'
import {View, Pressable} from 'react-native';
import { Icon, MaterialCommunityIcons } from 'apptile-core';
import { toolsMap } from '../../../sagas/frontendMcpTools';
import theme from '@/root/web/views/prompt-to-app/styles-prompt-to-app/theme';
import { arrowUpDown, thinLongRightArrow } from '@/root/web/views/prompt-to-app/editor/components/svgelems';
import { ActivityIndicator } from 'react-native-paper';
import { useSelector } from 'react-redux';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import Analytics from '@/root/web/lib/segment';

type AgentName = "Planner"|"Dev.";

export type ChatMessage = {
  id: number;
  chatId: number;
  role: 'user'|'assistant';
  content_type: 'text'|'tool_use'|'tool_result'|'text/plain'|'image/jpg'|'image/jpeg'|'image/png';
  content: string;
  tool_call_id: string | null;
  createdAt: Date;
  htmlcontent: string | null;
  tool_name: string | null;
  showing_details: boolean;
};

export type ChatHistory = {
  [key: string]: {
    chat: {
      id: number;
      llm_model: string;
      llm_provider: 'openai' | 'claude';
      outputFile: string;
      type: 'widget' | 'datasource';
    };
    messages: ChatMessage[];
  }
};

export function ProcessingText() {
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      paddingLeft: 16
    }}>
      <img 
        src={require("../../../assets/images/tiledevlogo.png")} 
        alt="Generated Image" 
        style={{width: 18, height: 18, filter: 'grayscale(1) brightness(1.5) opacity(0.5)'}} />
      <p style={{
        color: 'rgba(239, 239, 239, 0.40)',
        fontFamily: 'General Sans',
        fontSize: 16,
        fontStyle: 'normal',
        fontWeight: '400',
        lineHeight: '22px',
        display: 'flex',
        alignItems: 'center',
        gap: '2px',
        margin: 0
      }}>
        Processing
        <span style={{ display: 'inline-block', width: '18px', textAlign: 'left' }}>
          <span className="dot" style={{
            display: 'inline-block',
            animation: 'bounce 1.2s infinite',
            animationDelay: '0s',
          }}>.</span>
          <span className="dot" style={{
            display: 'inline-block',
            animation: 'bounce 1.2s infinite',
            animationDelay: '0.2s',
          }}>.</span>
          <span className="dot" style={{
            display: 'inline-block',
            animation: 'bounce 1.2s infinite',
            animationDelay: '0.4s',
          }}>.</span>
        </span>
      </p>
    </div>
  );
}

export type LiveMessageHandle = {
  [key: string]: {
    setMessageText: (text: string) => void;
    setToolText: (toolData: Array<{id: string; tool: string; partialInput: string;}>) => void;
    showMsgDiv: () => void;
    hide: () => void;
  }
};

function ToolPreview(props: {toolName: string;}) {
  return (<div
      style={{
        color: "white",
        borderWidth: 1,
        borderColor: "white",
        borderBottomColor: "white",
        borderStyle: 'solid',
        borderTopRightRadius: 8,
        borderTopLeftRadius: 8,
        borderBottomLeftRadius: 8,
        borderBottomRightRadius: 8,
        padding: 8,
        marginTop: 2,
        marginBottom: 2,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <div
        style={{
          background: 'linear-gradient(100deg, transparent 0%, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%, transparent 100%)',
          animation: 'shimmer 3s infinite',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0
        }}
      />
      <div 
        style={{ 
            display: 'flex', 
            flexDirection: 'row',
          }}
        >
        <img 
          src={require("../../../assets/images/ai.png")}
          style={{width: 20, height: 20, marginRight: 10}}
        />
        <div>
          {props.toolName ? "Preparing to call " + props.toolName : "Preparing command for apptile"}
        </div>
      </div>
    </div>
  );
}

const LiveMessage = forwardRef((props: {agentDisplayName: AgentName}, ref) => {
  const [visible, setVis] = useState(false);
  const [tools, setTools] = useState<Array<{id: string; tool: string; partialInput: string;}>>([]);
  const messageDiv = useRef<HTMLDivElement>(null);

  useImperativeHandle(ref, (): LiveMessageHandle => {
    return {
      setMessageText: (text: string) => {
        if (messageDiv.current) {
          messageDiv.current.innerHTML = text;
        }
      },
      setToolText: (toolData: Array<{id: string; tool: string; partialInput: string;}>) => {
        setTools(toolData);
      },
      showMsgDiv: () => {
        setVis(true);
      },
      hide: () => {
        setVis(false);
        if (messageDiv.current) {
          messageDiv.current.innerHTML = "";
        }
      }
    }
  }, [messageDiv, setTools]);

  return (
    <View
      nativeID="live-message-root"
      style={{
        display: visible ? "block" : "none",
        width: "100%",
      }}
    >
      <div style={{display: 'flex', flexDirection: 'column', marginTop: 12}}>
        <div style={{display: 'flex', alignItems: 'center'}}>
          <img 
            src={require("../../../assets/images/agent.png")}
            style={{
              width: 20,
              filter: props.agentDisplayName === "Dev." ? 'grayscale(1) brightness(1.5) opacity(0.5)' : "none",
            }}
          />
          <span style={{
            color: theme.FOREGROUND, 
            fontWeight: 500, 
            fontSize: 16,
            fontFamily: "General Sans",
            paddingLeft: theme.THIN_GAP
            }}
          >
            {props.agentDisplayName}
          </span>
        </div>
        <pre 
          ref={messageDiv}
          style={{
            display: visible ? "block" : "none",
            backgroundColor: "transparent",
            maxWidth: "100%",
            borderRadius: 12,
            color: theme.FOREGROUND,
            fontFamily: "General Sans",
            fontSize: 14,
            fontWeight: '400',
            padding: 17,
            paddingTop: 0,
            paddingLeft: 0,
            alignSelf: "auto",
            marginTop: 0,
            marginBottom: 12,
            whiteSpace: "pre-wrap",
          }}
        >
        </pre>
        {tools.map(tool => {
          if (tool.tool) {
            return <ToolPreview key={tool.id} toolName={tool.tool} />
          } else {
            return null;
          }
        })}
      </div>
    </View>
  );
})

export function TextMessage(props: {
  message: ChatMessage; 
  agentDisplayName: AgentName; 
  showAgentDisplayName: boolean;
  isFollowedByTool: boolean;
}) {
  const {message} = props;
  let result = null;
  const messageBg = {
    alignSelf: message.role === "user" ? "flex-end" : "auto",
    color: theme.FOREGROUND,
    backgroundColor: message.role === "user" ? theme.FG_SECONDARY : "transparent",
    paddingBottom: theme.PADDING,
    paddingTop: message.role === "user" ? theme.PADDING : 0,
    paddingLeft: message.role === "user" ? theme.PADDING : 0,
    paddingRight: message.role === "user" ? theme.PADDING : 0,
    marginBottom: props.isFollowedByTool ? 30 : 0,
    marginTop: theme.THIN_GAP,
    fontSize: 14,
    fontWeight: "400",
    fontFamily: "General Sans",
    borderRadius: theme.CORNER_RADIUS_HALF,
  };

  if (message.role === "user") {
    messageBg.maxWidth = "85%";
  }

  if (message.htmlcontent) {
    result = (
      <div
        className='user-chat-bubble'
        key={message.id}
        dangerouslySetInnerHTML={{ __html: message.htmlcontent || message.content }}
        style={messageBg}
      >
      </div>
    );
  } else {
    result = (
      <div
        className='user-chat-bubble'
        key={message.id}
        style={messageBg}
      >
        {message.content}
      </div>
    );
  }

  let agentDisplayName = null;
  if (props.showAgentDisplayName) {
    agentDisplayName = (<div style={{display: 'flex', alignItems: 'center'}}>
      <img
        src={require("../../../assets/images/agent.png")}
        style={{
          width: 20,
          filter: props.agentDisplayName === "Dev." ? 'grayscale(1) brightness(1.5) opacity(0.5)' : "none",
        }}
      />
      <span
        className='title-text'
        style={{
          paddingLeft: theme.THIN_GAP
        }}
      >
        {props.agentDisplayName}
      </span>
    </div>);
  }

  if (message.role === "assistant") {
    result = (
      <div 
        className='chat-bubble'
        style={{
          display: 'flex', 
          flexDirection: 'column', 
          wordBreak: "break-word", 
          paddingTop: 35,
        }}
      >
        {agentDisplayName}
        {result}
      </div>
    );
  }

  return result;
}

function ToolMessage(props: {toolUse: ChatMessage; toolResponse: null | ChatMessage;}) {
  const message = props.toolUse;
  const [expanded, toggleExpanded] = useState(false);
  const [hovered, setHovered] = useState(false);
  let title = "Calling: " + message.tool_name;
  let pluginName = "";
  if (message.tool_name && toolsMap.hasOwnProperty(message.tool_name)) {
    let header = toolsMap[message.tool_name].description;
    try {
      const inputData = JSON.parse(message.content);
      const variables = Object.keys(inputData);
      for (let i = 0; i < variables.length; ++i) {
        const key = variables[i];
        header = header.replace('__' + key + '__', inputData[key]);
      }
      pluginName = inputData?.name;
    } catch (err) {
      console.error("Unparsable input: ", message);
    }
    title = header;
  }

  let body = <></>
  if (expanded) {
    if (message.tool_name === "apply_file_ops") {
      try {
        const parsedBody = JSON.parse(message.content);
        const files = parsedBody.file_ops.map(file_op => {
          return <div>{file_op.path}</div>
        });
        body = (
          <div>
            <div>Editing these files</div>
            {files}
          </div>
        );
      } catch (err) {
        console.error("Error in parsing body");
        body = <div>{message.content}</div>
      }
    } else {
      body = <div>{message.content}</div>
    }
  }

  let renderedResponse = null;
  if (props.toolResponse && expanded) {
    renderedResponse = (
      <pre>{props.toolResponse.content}</pre>
    );
  }

  let result = (
    <Pressable
      onHoverIn={() => {setHovered(true)}}
      onHoverOut={() => {setHovered(false)}}
      style={{
        color: theme.FOREGROUND,
        borderRadius: theme.CORNER_RADIUS_HALF,
        marginTop: 2,
        marginBottom: 2,
        position: 'relative',
        background: theme.FG_SECONDARY
      }}
    >
      <div 
        style={{ 
          display: 'flex', 
          flexDirection: 'row', 
          height: 48, 
          alignItems: 'center',
          paddingLeft: theme.PADDING_SMALL,
          paddingRight: theme.PADDING_SMALL,
          borderRadius: theme.CORNER_RADIUS_HALF,
          backgroundColor: hovered ? theme.HOVER_COLOR : theme.FG_SECONDARY
        }}
        onClick={() => toggleExpanded(prev => !prev)}
      >
        <View
          style={{paddingHorizontal: theme.THIN_GAP}}
        >
          <Icon
            iconType='MaterialIcons'
            name='check-circle'
            size={20}
            style={{ color: 'white' }}
          />
        </View>
        <div className="body-text" style={{flex: 1}}>
          {title}
        </div>
        <View
          style={{paddingHorizontal: theme.THIN_GAP}}
        >
          {arrowUpDown}
        </View>
      </div>
      <div 
        className='body-text tool-call-body' 
        style={{
          flexDirection: 'column', 
          display: 'flex', 
          paddingLeft: theme.PADDING_SMALL + 36,
          paddingRight: theme.PADDING_SMALL,
          maxHeight: 200,
          overflowY: 'auto'
        }}
      >
        {body}
        {renderedResponse}
      </div>
    </Pressable>
  );
  
  return result;
}

export const DeveloperAgentToolMessage = forwardRef((props: {
  toolUse: ChatMessage; 
  toolResponse: null | ChatMessage; 
  chatHistory: ChatHistory;
  isLast: boolean;
}, ref) => {
  const {toolUse, toolResponse, chatHistory, isLast} = props;
  const [expanded, toggle] = useState(isLast);
  const [hovered, setHovered] = useState(false);
  const isChatRunning = useSelector((state: EditorRootState) => state.ai.isChatRunning);
  let result = null;
  const toolName = toolUse.tool_name;
  let header = "Using a tool: " + toolName;
  let pluginName = "";
  if (toolName && toolsMap.hasOwnProperty(toolName)) {
    header = toolsMap[toolName].description;
    try {
      const inputData = JSON.parse(toolUse.content);
      const variables = Object.keys(inputData);
      for (let i = 0; i < variables.length; ++i) {
        const key = variables[i];
        header = header.replace('__' + key + '__', inputData[key]);
      }
      pluginName = inputData?.name ?? "";
    } catch (err) {
      console.error("Unparsable input: ", toolUse);
    }
  }

  const collapsedContent = (
    <Pressable 
      style={{ 
        display: 'flex', 
        flexDirection: 'row', 
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 54
      }}
      onHoverIn={() => setHovered(true)}
      onHoverOut={() => setHovered(false)}
      onPress={() => toggle(prev => !prev)}
    >
      <span style={{display: "flex", flexDirection: "row"}}>
        {
          toolResponse && 
          (<Icon
            iconType='MaterialIcons'
            name='check-circle'
            size={20}
            style={{ color: 'white', marginRight: theme.THIN_GAP }}
          />)
        }
        <div style={{
          fontFamily: 'General Sans',
          fontSize: 14,
          fontWeight: '400'
        }}>
          {header}
        </div>
        {
          (!toolResponse && isLast && isChatRunning) && 
          (<ActivityIndicator 
            size="small" 
            color={theme.FOREGROUND} 
            style={{
              transform: [{scale: 0.6}, {translateY: -5}]
            }}
          />)
        }
      </span>
      <div
        // onClick={() => toggle(prev => !prev)}
        style={{flexDirection: "row", alignItems: "center", display: "flex"}}
      >
        <span 
          style={{
            backgroundColor: hovered ? "#12284B" : theme.FG_SECONDARY,
            borderRadius: 6,
            paddingLeft: 10,
            paddingRight: 10,
            paddingTop: 3,
            paddingBottom: 3,
            fontSize: 12,
            fontWeight: '300',
            fontFamily: 'General Sans',
            color: theme.ACCENT_LIGHT
          }}
        >
          Dev. Agent
        </span>
        <MaterialCommunityIcons name="chevron-down" size={24} color={theme.FOREGROUND} />
      </div>
    </Pressable>
  );

  let expandedContent = null;

  logger.info("Processing for: " + pluginName);
  if (expanded && pluginName) {
    const pluginChat = chatHistory[pluginName];
    logger.info("Found pluginchat: ", pluginChat)
    if (pluginChat) {
      expandedContent = (
        <ChatThread  
          messages={pluginChat?.messages ?? []}
          chatHistory={null}
          setChatHistory={() => {}}
          refkey={isLast ? pluginName : ""}
          showPlannerHeader={true}
          ref={ref}
        />
      );
    }
  }

  result = (
    <div
      className='chat-bubble dev-agent'
      style={{
        borderRadius: theme.CORNER_RADIUS_HALF,
        flexDirection: "column",
        display: "flex",
        alignItems: "stretch",
        justifyContent: "center",
        borderWidth: 1,
        borderStyle: "solid",
        borderColor: theme.FOREGROUND_OP_HALF,
        paddingLeft: theme.THIN_GAP * 2,
        paddingRight: theme.THIN_GAP * 2,
        marginBottom: theme.THIN_GAP * 2,
      }}
    >
      {collapsedContent}
      <div
        className={expandedContent ? "thin-scrollbar expanded-agent-chat" : "thin-scrollbar"}
        style={{
          maxHeight: 300,
          overflowY: "auto",
          marginRight: -11,
          paddingRight: 8
        }}
      >
        {expandedContent}
      </div>
    </div>
  );
  return result;
});

export function ChatMessage(props: {
  message: ChatMessage, 
  onToggleDetail: () => void,
  chatHistory: ChatHistory | null
}) {
  const {message} = props;
  let result: ReactElement<any, any>;
  result = (
    <div
      style={{
        color: "white"
      }}
    >
      Not a renderable message. Content type: {message.content_type}
    </div>
  );

  return result;
}

function PlannerHeader(props: {message1: ChatMessage; message2?: ChatMessage}) {
  const message1 = props.message1;
  const message2 = props.message2;

  return (
    <div
      style={{
        backgroundColor: theme.FG_SECONDARY,
        fontSize: 14,
        fontFamily: "General Sans",
        fontWeight: "400",
        padding: theme.PADDING,
        borderRadius: theme.CORNER_RADIUS_HALF 
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          gap: theme.THIN_GAP,
          alignItems: "center"
        }}
      >
        <img
          src={require("../../../assets/images/agent.png")}
          style={{
            width: 20,
          }}
        />
        <span className='title-text'>Planner</span>
        <span style={{paddingLeft: theme.THIN_GAP, paddingRight: theme.THIN_GAP}}>
          {thinLongRightArrow}
        </span>
        {/* <Icon
          iconType='FontAwesome'
          name='long-arrow-right'
          size={15}
          style={{
            paddingHorizontal: theme.THIN_GAP
          }}
        /> */}
        <img
          src={require("../../../assets/images/agent.png")}
          style={{
            width: 20,
            filter: 'grayscale(1) brightness(1.5) opacity(0.5)',
          }}
        />
        <span className='title-text'>Dev.</span>
      </div>
      <div
        style={{ fontStyle: "italic" }}
        dangerouslySetInnerHTML={{ __html: message1.htmlcontent || message1.content }}
      />
      {message2 && (
        <div
          style={{ fontStyle: "italic" }}
          dangerouslySetInnerHTML={{ __html: (message2.htmlcontent || message2.content) }}
        />
      )}
    </div>
  );
}

export const ChatThread = forwardRef((props: {
  messages: ChatMessage[];
  chatHistory: ChatHistory|null;
  setChatHistory: any;
  showPlannerHeader: boolean;
  refkey: string;
}, ref) => {
  const {messages, chatHistory, setChatHistory} = props;
  let renderedMessages: Array<ReactElement<any, any>> = [];

  for (let i = 0; i < messages.length;) {
    const message = messages[i];
    let shouldShowAgentDisplayName = false;
    if (i > 0) {
      shouldShowAgentDisplayName = ((messages[i - 1].role !== "assistant") && (messages[i - 1].content_type !== "tool_result")) && messages[i].role === "assistant";
    }
    let result = null;

    if (message.content.startsWith('The name of the plugin you are working on is:')) {
      if (messages.length > 1) {
        result = (<PlannerHeader 
          message1={messages[0]}
          message2={messages[1]}
        />);
        i += 2;
      } else {
        result = (<PlannerHeader 
          message1={messages[0]}
        />);
        i += 1;
      }
    } else if (message.content_type === "text") {
      let followedByTool = false;
      if (messages[i + 1]) {
        followedByTool = !!messages[i + 1].tool_call_id;
      }
      result = (
        <TextMessage 
          message={message} 
          agentDisplayName={props.refkey === "planner" ? "Planner" : "Dev."} 
          showAgentDisplayName={shouldShowAgentDisplayName}
          isFollowedByTool={followedByTool}
        />
      );
      i++;
    } else if (message.content_type.startsWith("image/")) {
      result = (
        <img 
          style={{
            maxWidth: 163, 
            borderRadius: theme.CORNER_RADIUS_HALF, 
            alignSelf: 'flex-end',
            marginTop: 16,
            marginBottom: 16
          }} 
          src={`data:${message.content_type};base64,${message.content}`}
        />
      );
      i++;
    } else if (message.content_type === "tool_use") {
      const toolUse = message;
      let toolResponse = null;
      if (messages[i + 1]?.content_type === "tool_result") {
        toolResponse = messages[i + 1] || null;
        i += 2;
      } else {
        i += 1;
      }
      if ((message.tool_name === "nocodelayer_generate_code_for_plugin") && chatHistory) {
        result = (
          <DeveloperAgentToolMessage 
            toolUse={toolUse} 
            toolResponse={toolResponse} 
            chatHistory={chatHistory} 
            isLast={i + 1 >= messages.length}
            ref={ref}
          />
        );
      } else {
        result = (
          <ToolMessage
            toolUse={toolUse}
            toolResponse={toolResponse}
          />
        );
      }
    } else {
      result = (<ChatMessage
        message={message}
        chatHistory={chatHistory}
        onToggleDetail={() => {
          Analytics.track('dev_agent_chat_viewed');
          setChatHistory(prev => {
            const newMessages = (prev?.planner?.messages ?? []).slice();
            newMessages[i].showing_details = !newMessages[i].showing_details;
            if (newMessages[i + 1]) {
              newMessages[i + 1].showing_details = !newMessages[i + 1].showing_details;
            }
            return {
              ...prev,
              planner: {
                chat: prev?.planner?.chat,
                messages: newMessages
              }
            };
          });
        }}
      />);
      i++;
    }
    renderedMessages.push(result);
  }

  return (
    <>
      {renderedMessages}
      <LiveMessage 
        ref={c => {
          if (props.refkey === "planner") {
            if (ref && !ref?.current) {
              ref.current = {planner: c};
            } else if (ref && ref.current) {
              ref.current.planner = c;
            }
          } else if (props.refkey) {
            if (ref && !ref?.current) {
              ref.current = {plugin: c, pluginName: props.refkey};
            } else if (ref && ref.current) {
              ref.current.plugin = c;
              ref.current.pluginName = props.refkey;
            }
          }
        }}
        agentDisplayName={props.refkey === "planner" ? "Planner" : "Dev."}
      />
    </>
  );
});