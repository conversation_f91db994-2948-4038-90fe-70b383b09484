import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Pressable, Modal, ActivityIndicator, ScrollView} from 'react-native';
import {useSelector} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '../../components-v2/base/TextElement';
import {PADDLE_CLIENT_TOKEN} from '@/root/.env.json';
import Button from '../../components-v2/base/Button';
import {PaddlePlan, getPaddleService} from '../../services/PaddleService';
import {PaddleProduct, SubscriptionStatus, SubscriptionWithPlan} from '../../api/ApiTypes';
import {EditorRootState} from '../../store/EditorRootState';
import theme from '../../styles-v2/theme';
import PaddleApi from '../../api/PaddleApi';
import {useParams} from 'react-router';
import CurrentPlanDetails from './CurrentPlanDetails';
import {useNavigate} from '../../routing.web';

interface PricingModalProps {
  visible: boolean;
  onClose: () => void;
}

const PricingModal: React.FC<PricingModalProps> = ({visible, onClose}) => {
  const [products, setProducts] = useState<PaddleProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<PaddlePlan | null>(null);
  const [_selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);
  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionWithPlan | null>(null);
  const [creatingFreePlan, setCreatingFreePlan] = useState(false);

  const {user} = useSelector((state: EditorRootState) => state.user);
  const orgId = useParams<{orgId: string}>().orgId;

  useEffect(() => {
    if (visible) {
      loadProducts();
      loadCurrentSubscription();
    } else {
      // Close dropdown when modal closes
      setDropdownOpen(null);
    }
  }, [visible, orgId]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError(null);

      const paddleService = getPaddleService(PADDLE_CLIENT_TOKEN);
      await paddleService.initialize();
      const fetchedProducts = await paddleService.fetchProductsWithPlans();
      setProducts(fetchedProducts);

      // Auto-select the first product if available and set default plan
      if (fetchedProducts.length > 0) {
        const firstProduct = fetchedProducts[0];
        setSelectedProductId(firstProduct.id);

        // Set the first monthly plan as default
        const monthlyPlans = firstProduct.plans.filter((p: any) => p.billing_cycle.interval === 'month');
        if (monthlyPlans.length > 0) {
          setSelectedPlan(monthlyPlans[0]);
        }
      }
    } catch (err) {
      console.error('Failed to load products:', err);
      setError('Failed to load pricing plans. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentSubscription = async () => {
    if (!orgId) return;

    try {
      const response = await PaddleApi.getOrganizationSubscriptionsWithPlans(orgId.toString());
      const subscriptions = response.data;

      // Find the active subscription
      const activeSubscription = subscriptions.find(
        (sub: SubscriptionWithPlan) => sub.status === SubscriptionStatus.ACTIVE,
      );

      setCurrentSubscription(activeSubscription || null);
      setSelectedPlan(activeSubscription?.plan || null);
    } catch (err) {
      console.error('Failed to load current subscription:', err);
    }
  };

  //Listen to the window event purchaseCompleted and trigger loadCurrentSubscription
  useEffect(() => {
    const handlePurchaseCompleted = () => {
      loadCurrentSubscription();
      setLoading(false);
    };

    const handleFailedPurchase = () => {
      setLoading(false);
      //Show error message
      setError('Failed to complete purchase. Please try again.');
    };

    window.addEventListener('purchaseCompleted', handlePurchaseCompleted as EventListener);
    window.addEventListener('purchaseInProgress', (() => setLoading(true)) as EventListener);
    window.addEventListener('purchaseFailed', handleFailedPurchase as EventListener);

    return () => {
      window.removeEventListener('purchaseCompleted', handlePurchaseCompleted as EventListener);
    };
  }, []);

  const hasActiveSubscription = (): boolean => {
    return currentSubscription !== null && currentSubscription.status === SubscriptionStatus.ACTIVE;
  };

  const handleUpgrade = async (plan: PaddlePlan) => {
    try {
      const paddleService = getPaddleService();
      await paddleService.openCheckout({
        items: [
          {
            priceId: plan.id,
            quantity: 1,
          },
        ],
        customer: {
          email: user?.email,
        },
        customData: {
          organizationId: orgId?.toString(),
          userId: user?.id?.toString(),
          credits: plan.custom_data?.credits,
        },
      });
    } catch (err) {
      console.error('Failed to open checkout:', err);
    }
  };

  const handleFreePlanSelection = async () => {
    if (!orgId || isCurrentFreePlan) return;

    try {
      setCreatingFreePlan(true);
      await PaddleApi.createFreePlanSubscription(orgId);

      // Reload current subscription to reflect the new free plan
      await loadCurrentSubscription();

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('purchaseCompleted'));
    } catch (error) {
      console.error('Failed to create free plan subscription:', error);
      // You might want to show an error message to the user
    } finally {
      setCreatingFreePlan(false);
    }
  };

  // Check if user is on free plan (has active subscription but with null paddle IDs)
  const isCurrentFreePlan =
    hasActiveSubscription() &&
    currentSubscription &&
    !currentSubscription.paddleSubscriptionId &&
    !currentSubscription.paddlePriceId;

  // Render Free Plan Card (hardcoded)
  const renderFreePlanCard = () => {
    return (
      <View style={[styles.planCard, styles.freePlanCard, isCurrentFreePlan && styles.currentPlanCard]}>
        <View style={styles.planHeader}>
          <View style={styles.planTitleContainer}>
            {isCurrentFreePlan && (
              <View style={styles.currentPlanTag}>
                <TextElement fontSize="xs" style={{color: '#4CAF50', fontSize: 11}}>
                  CURRENT
                </TextElement>
              </View>
            )}
            <TextElement fontSize="xl" fontWeight="500" style={{color: '#FCFBF8'}}>
              Free
            </TextElement>
          </View>
        </View>

        <View style={styles.priceSection}>
          <View style={styles.priceRow}>
            <TextElement fontSize="3xl" fontWeight="500" style={{color: '#FFFFFF'}}>
              $0
            </TextElement>
            <TextElement fontSize="md" style={{color: '#BCBCBC', marginRight: 4}}>
              /month
            </TextElement>
          </View>
          <TextElement fontSize="sm" style={{color: '#BCBCBC', marginTop: 4}}>
            {isCurrentFreePlan ? 'Your current plan' : 'For getting started'}
          </TextElement>

          {/* Current Free Plan Details - Always show if user has any subscription with null paddle IDs */}
          <CurrentPlanDetails currentSubscription={currentSubscription} isFreePlan={true} />
        </View>

        {/* Credits display similar to dropdown but static */}
        <View
          style={[
            styles.planDropdownContainer,
            {
              backgroundColor: '#0F1114',
              height: 42,
              borderRadius: 6,
              justifyContent: 'center',
              alignItems: 'flex-start',
            },
          ]}>
          <TextElement fontSize="sm" fontWeight="500" style={{color: '#BCBCBC', marginLeft: 13}}>
            1000 monthly credits - $0/month
          </TextElement>
        </View>

        {/* Get Started Button - moved here, right after credits */}
        <View style={styles.buttonContainer}>
          <Button
            color={isCurrentFreePlan ? 'CTA' : hasActiveSubscription() ? 'SECONDARY' : 'CTA'}
            size="MEDIUM"
            onPress={() => {
              if (!isCurrentFreePlan) {
                handleFreePlanSelection();
              }
            }}
            containerStyles={[styles.selectButton, isCurrentFreePlan && styles.currentPlanButton]}
            disabled={isCurrentFreePlan || creatingFreePlan}>
            {creatingFreePlan ? 'Creating...' : isCurrentFreePlan ? 'Current Plan' : hasActiveSubscription() ? 'Downgrade' : 'Get Started'}
          </Button>
        </View>

        {/* Features section - moved after button */}
        <TextElement fontSize="sm" fontWeight="400" style={[styles.featuresTitle, {color: '#FFFFFF'}]}>
          Get started with:
        </TextElement>

        <View style={styles.featuresList}>
          {[
            'Unlimited projects',
            'Visual Editing',
            'Chat support via Discord',
            'Limited access to Advanced Coding Model',
          ].map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <MaterialCommunityIcons name="check" size={16} color={theme.SUCCESS} style={styles.checkIcon} />
              <TextElement fontSize="sm" style={[styles.featureText, {color: '#C5C1BA'}]}>
                {feature}
              </TextElement>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Parse features from product description (comma-separated)
  const parseFeatures = (description: string | undefined): string[] => {
    if (!description) return [];
    return description
      .split(',')
      .map(feature => feature.trim())
      .filter(feature => feature.length > 0);
  };

  // Render Dynamic Product Card with dropdown
  const renderProductCard = (product: PaddleProduct, isPopular: boolean = false) => {
    // Get all plans for this product (both monthly and yearly)
    const allPlans = product.plans || [];
    const monthlyPlans = allPlans.filter(p => p.billing_cycle.interval === 'month');

    // Default to first monthly plan if no plan is selected for this product
    const currentSelectedPlan = selectedPlan && selectedPlan.product_id === product.id ? selectedPlan : monthlyPlans[0];

    const handlePlanSelect = (plan: PaddlePlan) => {
      setSelectedPlan(plan);
      setDropdownOpen(null);
    };

    // Parse features from product description
    const features = parseFeatures(product.description);

    // Check if this is an upgrade option based on selected plan price
    const isUpgrade =
      hasActiveSubscription() &&
      currentSubscription &&
      parseFloat(currentSelectedPlan?.unit_price.amount || '0') > (currentSubscription.purchasePrice || 0);

    // Check if this is the current plan and same price as purchased
    const isCurrentPlan =
      hasActiveSubscription() &&
      currentSubscription &&
      currentSubscription.plan?.product_id === product.id &&
      parseFloat(currentSelectedPlan?.unit_price.amount || '0') ===
        parseFloat(String(currentSubscription.purchasePrice || 0));

    return (
      <View key={product.id} style={[styles.planCard, isCurrentPlan && styles.currentPlanCard]}>
        <View style={styles.planHeader}>
          <View style={styles.planTitleContainer}>
            {isCurrentPlan && (
              <View style={styles.currentPlanTag}>
                <TextElement fontSize="xs" style={{color: '#4CAF50', fontSize: 11}}>
                  CURRENT
                </TextElement>
              </View>
            )}
            <TextElement fontSize="xl" fontWeight="500" style={{color: '#FCFBF8'}}>
              {product.name}
            </TextElement>
            {isPopular && !isCurrentPlan && (
              <View style={styles.popularTag}>
                <TextElement fontSize="xs" style={{color: '#BDC2FF', fontSize: 11}}>
                  POPULAR
                </TextElement>
              </View>
            )}
          </View>
        </View>

        <View style={styles.priceSection}>
          <View style={styles.priceRow}>
            <TextElement fontSize="3xl" fontWeight="500" style={{color: '#FFFFFF'}}>
              ${currentSelectedPlan?.unit_price.amount || '50'}
            </TextElement>
            <TextElement fontSize="md" style={{color: '#BCBCBC', marginRight: 4}}>
              /{currentSelectedPlan?.billing_cycle.interval || 'month'}
            </TextElement>
          </View>
          <TextElement fontSize="sm" style={{color: '#BCBCBC', marginTop: 4}}>
            {isCurrentPlan ? 'Your current plan' : 'For more projects and usage'}
          </TextElement>

          {/* Current Plan Details */}
          {!isUpgrade && (
            <CurrentPlanDetails
              currentSubscription={currentSubscription}
              isFreePlan={false}
              planPrice={parseFloat(currentSelectedPlan?.unit_price.amount || '0')}
              billingInterval={currentSelectedPlan?.billing_cycle.interval}
            />
          )}

          {/* Upgrade Information */}
          {isUpgrade && (
            <View style={styles.upgradeInfo}>
              <TextElement fontSize="xs" style={{color: '#BDC2FF', marginTop: 8}}>
                ⬆️ Upgrade from your current ${currentSubscription?.purchasePrice}/
                {currentSubscription?.billingInterval === 'month' ? 'month' : 'year'} plan
              </TextElement>
            </View>
          )}
        </View>

        {/* Dropdown for plan selection */}
        <View style={styles.planDropdownContainer}>
          <Pressable
            style={styles.planDropdown}
            onPress={() => setDropdownOpen(dropdownOpen === product.id ? null : product.id)}>
            <TextElement fontSize="sm" fontWeight="600" style={{color: '#BCBCBC'}}>
              {currentSelectedPlan?.custom_data?.credits || '5000'} credits - $
              {currentSelectedPlan?.unit_price.amount || '50'}/{currentSelectedPlan?.billing_cycle.interval || 'month'}
            </TextElement>
            <View style={[styles.chevronIcon, dropdownOpen === product.id && styles.chevronIconRotated]}>
              <MaterialCommunityIcons name="chevron-down" size={16} color="#BCBCBC" />
            </View>
          </Pressable>

          {/* Dropdown Options */}
          {dropdownOpen === product.id && (
            <View style={styles.dropdownOptions}>
              <ScrollView
                style={styles.dropdownScrollView}
                showsVerticalScrollIndicator={false}
                nestedScrollEnabled={true}>
                {allPlans.map((plan, index) => (
                  <Pressable
                    key={plan.id}
                    style={[
                      styles.dropdownOption,
                      currentSelectedPlan?.id === plan.id && styles.dropdownOptionSelected,
                      index === allPlans.length - 1 && styles.dropdownOptionLast,
                    ]}
                    onPress={() => handlePlanSelect(plan)}>
                    <View style={styles.dropdownOptionContent}>
                      <View>
                        <TextElement fontSize="sm" fontWeight="500" style={{color: '#FFFFFF'}}>
                          {plan.custom_data?.credits || plan.custom_data?.tokens || '0'} credits
                        </TextElement>
                        <TextElement fontSize="xs" style={{color: '#BCBCBC', marginTop: 2}}>
                          ${plan.unit_price.amount}/{plan.billing_cycle.interval}
                        </TextElement>
                      </View>

                      {/* Render a tag saying current purchased plan */}
                      {currentSubscription?.plan?.id === plan.id && (
                        <View style={styles.currentPlanTag}>
                          <TextElement fontSize="xs" style={{color: '#4CAF50', fontSize: 11}}>
                            CURRENT
                          </TextElement>
                        </View>
                      )}
                    </View>
                  </Pressable>
                ))}
              </ScrollView>
            </View>
          )}
        </View>

        {/* Get Started Button - moved here, right after dropdown */}
        <View style={styles.buttonContainer}>
          <Button
            color={isCurrentPlan ? 'SECONDARY' : !hasActiveSubscription() ? 'CTA' : !isUpgrade && !isCurrentFreePlan ? 'SECONDARY' : 'CTA'}
            size="MEDIUM"
            onPress={() => currentSelectedPlan && !isCurrentPlan && handleUpgrade(currentSelectedPlan)}
            containerStyles={[styles.selectButton, isCurrentPlan && styles.currentPlanButton]}
            disabled={isCurrentPlan || false}>
            {isCurrentPlan ? 'Current Plan' : isUpgrade || isCurrentFreePlan ? 'Upgrade' : hasActiveSubscription() ? 'Downgrade' : 'Get Started'}
          </Button>
        </View>

        {/* Dynamic Features from Product Description - moved after button */}
        {features.length > 0 && (
          <>
            <TextElement fontSize="sm" fontWeight="400" style={[styles.featuresTitle, {color: '#FFFFFF'}]}>
              Everything in Free, plus:
            </TextElement>

            <View style={styles.featuresList}>
              {features.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <MaterialCommunityIcons name="check" size={16} color={theme.SUCCESS} style={styles.checkIcon} />
                  <TextElement fontSize="sm" style={[styles.featureText, {color: '#C5C1BA'}]}>
                    {feature}
                  </TextElement>
                </View>
              ))}
            </View>
          </>
        )}
      </View>
    );
  };

  const navigate  = useNavigate();

  // Render Enterprise Plan Card (hardcoded)
  const renderEnterprisePlanCard = () => (
    <View style={[styles.planCard, styles.enterprisePlanCard]}>
      <View style={styles.planHeader}>
        <View style={styles.planTitleContainer}>
          <TextElement fontSize="xl" fontWeight="500" style={{color: '#FCFBF8'}}>
            Enterprise
          </TextElement>
        </View>
      </View>

      <View style={styles.priceSection}>
        <View style={styles.priceRow}>
          <TextElement fontSize="3xl" fontWeight="500" style={{color: '#FFFFFF'}}>
            Custom
          </TextElement>
        </View>
        <TextElement fontSize="sm" style={{color: '#BCBCBC', marginTop: 4}}>
          For collaborating with others
        </TextElement>
      </View>

      {/* Credits display similar to dropdown but static */}
      <View
        style={[
          styles.planDropdownContainer,
          {
            backgroundColor: '#0F1114',
            height: 42,
            borderRadius: 6,
            justifyContent: 'center',
            alignItems: 'flex-start',
          },
        ]}>
        <TextElement fontSize="sm" fontWeight="500" style={{color: '#BCBCBC', marginLeft: 13}}>
          Unlimited credits - Custom pricing
        </TextElement>
      </View>

      {/* Book a Call Button - moved here, right after credits */}
      <View style={styles.buttonContainer}>
        <Button
          color="SECONDARY"
          size="MEDIUM"
          onPress={() => {
            navigate('/book-a-call');
          }}
          containerStyles={[styles.selectButton, styles.enterpriseButton]}>
          <View style={styles.enterpriseButtonContent}>
            <MaterialCommunityIcons name="calendar" size={18} color={theme.DEFAULT_COLOR} style={{marginRight: 8}} />
            <TextElement fontSize="sm" fontWeight="600" style={{color: '#FFFFFF'}}>
              Book a Call
            </TextElement>
          </View>
        </Button>
      </View>

      {/* Features section - moved after button */}
      <TextElement fontSize="sm" fontWeight="400" style={[styles.featuresTitle, {color: '#FFFFFF'}]}>
        Everything in Pro, plus:
      </TextElement>

      <View style={styles.featuresList}>
        {[
          'Custom Agents',
          'Human experts to lead your project',
          'Tile SDK (Code export and Developer tools)',
          'Premium Support',
          'No rate limits',
        ].map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <MaterialCommunityIcons name="check" size={16} color={theme.SUCCESS} style={styles.checkIcon} />
            <TextElement fontSize="sm" style={[styles.featureText, {color: '#C5C1BA'}]}>
              {feature}
            </TextElement>
          </View>
        ))}
      </View>
    </View>
  );

  const onRetryPress = async () => {
    //Call the sync subscription api
    await PaddleApi.syncOrganizationSubscriptions(orgId);
    loadProducts();
  };

  const renderContent = () => (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Pressable onPress={onClose} style={styles.closeButton}>
          <MaterialCommunityIcons name="close" size={24} color="#FFFFFF" />
        </Pressable>
        <TextElement fontSize="3xl" fontWeight="600" style={[styles.title, {color: '#FFFFFF'}]}>
          Pricing
        </TextElement>
        <TextElement fontSize="lg" style={[styles.subtitle, {color: '#BCBCBC'}]}>
          Start for free. Upgrade to get the capacity that exactly matches your team's needs.
        </TextElement>
      </View>

      {/* Plans Container */}
      <Pressable style={styles.plansContainer} onPress={() => setDropdownOpen(null)}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.CTA} />
            <TextElement fontSize="lg" style={{marginTop: 16, color: '#BCBCBC'}}>
              Loading...
            </TextElement>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <MaterialCommunityIcons name="alert-circle" size={48} color={theme.ERROR} />
            <TextElement fontSize="lg" style={{marginTop: 16, textAlign: 'center', color: '#FF6B6B'}}>
              {error}
            </TextElement>
            <Button color="CTA" size="MEDIUM" onPress={onRetryPress} containerStyles={{marginTop: 16}}>
              Try Again
            </Button>
          </View>
        ) : (
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.plansScrollContainer}
            style={styles.plansScrollView}>
            {renderFreePlanCard()}
            {products.map((product, index) => renderProductCard(product, index === 0))}
            {renderEnterprisePlanCard()}
          </ScrollView>
        )}
      </Pressable>
    </View>
  );

  return (
    <Modal visible={visible} transparent={true} animationType="fade" onRequestClose={onClose}>
      <Pressable style={styles.modalBackground} onPress={onClose}>
        <Pressable style={styles.modalContainer} onPress={e => e.stopPropagation()}>
          {renderContent()}
        </Pressable>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: 'auto',
    minWidth: 1024,
    maxWidth: '95vw',
    maxHeight: '90vh',
    backgroundColor: '#0F1114',
    borderRadius: 20,
    padding: 0,
    overflow: 'hidden',
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: {width: 0, height: 20},
    shadowOpacity: 0.15,
    shadowRadius: 40,
    elevation: 20,
    display: 'flex',
    flexDirection: 'column',
  },
  header: {
    padding: 24,
    paddingBottom: 20,
    alignItems: 'center',
    flexShrink: 0,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    padding: 12,
    zIndex: 1,
    borderRadius: 8,
    backgroundColor: '#2A2D31',
  },
  title: {
    marginBottom: 12,
    textAlign: 'center',
    fontSize: 29,
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 600,
    lineHeight: 24,
    fontSize: 15,
  },
  plansContainer: {
    flex: 1,
    paddingBottom: 20,
    overflow: 'hidden',
  },
  plansScrollView: {
    flex: 1,
  },
  plansScrollContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 32,
    minWidth: '100%',
  },
  plansGrid: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
    paddingHorizontal: 40,
  },
  planCard: {
    width: 317,
    backgroundColor: '#1A1D20',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5C5C5C',
    padding: 20,
    position: 'relative',
    minHeight: 480,
    maxHeight: 620,
    marginHorizontal: 12,
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    display: 'flex',
    flexDirection: 'column',
  },
  freePlanCard: {
    // Additional styles for free plan if needed
  },
  currentPlanCard: {
    borderColor: theme.SUCCESS,
    borderWidth: 2,
    backgroundColor: '#1A1F1A',
  },
  upgradePlanCard: {
    borderColor: theme.CTA,
    borderWidth: 2,
  },
  proPlanCard: {
    borderColor: theme.CTA,
    borderWidth: 3,
  },
  popularPlanCard: {
    borderColor: theme.CTA,
    borderWidth: 3,
  },
  enterprisePlanCard: {
    // Additional styles for enterprise plan if needed
  },
  planHeader: {
    marginBottom: 16,
  },
  planTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 10,
  },
  popularTag: {
    backgroundColor: '#2B2A47',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  currentPlanTag: {
    backgroundColor: '#1B2E1B',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  currentPlanDetails: {
    backgroundColor: '#1A2A1A',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#2A4A2A',
  },
  upgradeInfo: {
    backgroundColor: '#1A1F2A',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#2A2F4A',
  },
  planTitle: {
    marginBottom: 20,
  },
  planSubtitle: {
    marginTop: 8,
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 0,
    right: 0,
    backgroundColor: '#2B2A47',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    alignItems: 'center',
  },
  creditsContainer: {
    backgroundColor: '#0F1114',
    borderRadius: 6,
    paddingVertical: 10,
    paddingHorizontal: 13,
    marginVertical: 16,
  },
  planDropdownContainer: {
    marginVertical: 12,
    position: 'relative',
    zIndex: 1000,
  },
  planDropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#1A1D20',
    borderWidth: 1,
    borderColor: '#5C5C5C',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  dropdownOptions: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: '#1A1D20',
    borderWidth: 1,
    borderColor: '#5C5C5C',
    borderRadius: 8,
    marginBottom: 8,
    zIndex: 9999,
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: -4},
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 15,
    maxHeight: 200,
    overflow: 'hidden',
  },
  dropdownScrollView: {
    maxHeight: 200,
  },
  dropdownOption: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#3A3D41',
  },
  dropdownOptionSelected: {
    backgroundColor: '#2B2F33',
    borderLeftWidth: 3,
    borderLeftColor: theme.CTA,
  },
  dropdownOptionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownOptionLast: {
    borderBottomWidth: 0,
  },
  chevronIcon: {},
  chevronIconRotated: {
    transform: [{rotate: '180deg'}],
  },
  priceSection: {
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  creditsText: {
    textAlign: 'left',
  },
  featuresTitle: {
    marginBottom: 8,
  },
  featuresList: {
    marginBottom: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  checkIcon: {
    marginRight: 12,
    color: '#FFFFFF',
  },
  featureText: {
    flex: 1,
    lineHeight: 20,
  },
  buttonContainer: {
    marginTop: 12,
    marginBottom: 12,
  },
  selectButton: {
    width: '100%',
  },
  currentPlanButton: {
    backgroundColor: '#2A2D31',
    borderWidth: 1,
    borderColor: '#5C5C5C',
  },
  enterpriseButton: {
    backgroundColor: 'linear-gradient(135deg, #114DAD 0%, #161616 100%)',
    borderWidth: 1,
    borderColor: theme.DEFAULT_COLOR,
  },
  enterpriseButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  currentSubscriptionContainer: {
    backgroundColor: '#1A1D20',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.SUCCESS,
    padding: 16,
    marginTop: 20,
    maxWidth: 400,
  },
  currentSubscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentSubscriptionDetails: {
    marginBottom: 12,
  },
  currentSubscriptionActions: {
    alignItems: 'flex-start',
  },
  manageButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
});

export default PricingModal;
