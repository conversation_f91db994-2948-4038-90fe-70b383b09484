import React, {useState} from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';
import {Image} from 'react-native-svg';
import Analytics from '@/root/web/lib/segment';

interface SuggestionListProps {
  onSuggestionClick: (suggestion: string) => void;
}

const suggestions = [
  {name: 'Health App', prompt: 'Build a simple health app'},
  {name: 'Food Recipe App', prompt: 'Build a simple food recipe app'},
  {name: 'Weather App', prompt: 'Build a simple weather app'},
  {name: 'Calendar App', prompt: 'Build a simple calendar app'},
];

const HealthAppIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="clamp(20px, 4vw, 25px)"
    height="clamp(20px, 4vw, 24px)"
    viewBox="0 0 25 24"
    fill="none">
    <g>
      <path
        d="M14.3878 8.25C14.9811 8.25 15.5611 8.07405 16.0545 7.74441C16.5478 7.41477 16.9324 6.94623 17.1594 6.39805C17.3865 5.84987 17.4459 5.24667 17.3301 4.66473C17.2144 4.08279 16.9287 3.54824 16.5091 3.12868C16.0895 2.70912 15.555 2.4234 14.973 2.30765C14.3911 2.19189 13.7879 2.2513 13.2397 2.47836C12.6915 2.70543 12.223 3.08994 11.8934 3.58329C11.5637 4.07664 11.3878 4.65666 11.3878 5.25C11.3878 6.04565 11.7038 6.80871 12.2665 7.37132C12.8291 7.93393 13.5921 8.25 14.3878 8.25ZM14.3878 3.75C14.6844 3.75 14.9745 3.83797 15.2211 4.0028C15.4678 4.16762 15.6601 4.40189 15.7736 4.67598C15.8871 4.95007 15.9168 5.25167 15.859 5.54264C15.8011 5.83361 15.6582 6.10088 15.4484 6.31066C15.2387 6.52044 14.9714 6.6633 14.6804 6.72118C14.3894 6.77906 14.0878 6.74935 13.8138 6.63582C13.5397 6.52229 13.3054 6.33003 13.1406 6.08336C12.9757 5.83668 12.8878 5.54667 12.8878 5.25C12.8878 4.85218 13.0458 4.47065 13.3271 4.18934C13.6084 3.90804 13.99 3.75 14.3878 3.75ZM20.6981 13.1888C20.6409 13.215 19.9959 13.4963 18.854 13.4963C17.5556 13.4963 15.615 13.1325 13.1643 11.6213C12.7913 12.6801 12.3071 13.6964 11.7197 14.6531C12.7748 14.9779 13.7672 15.4791 14.655 16.1353C16.4428 17.4984 17.3878 19.4391 17.3878 21.75C17.3878 21.9489 17.3088 22.1397 17.1681 22.2803C17.0275 22.421 16.8367 22.5 16.6378 22.5C16.4389 22.5 16.2481 22.421 16.1074 22.2803C15.9668 22.1397 15.8878 21.9489 15.8878 21.75C15.8878 17.8406 12.6356 16.4334 10.8122 15.9516C10.7606 16.0172 10.7072 16.0838 10.6537 16.1484C8.81246 18.3797 6.50528 19.5403 3.9384 19.5403C3.64603 19.5417 3.35378 19.5282 3.06278 19.5C2.86386 19.4801 2.681 19.382 2.55441 19.2273C2.42783 19.0726 2.36788 18.8739 2.38778 18.675C2.40767 18.4761 2.50576 18.2932 2.66048 18.1666C2.8152 18.0401 3.01386 17.9801 3.21278 18C5.64278 18.2419 7.75684 17.2978 9.49403 15.1875C10.665 13.7681 11.4628 12.0366 11.8612 10.7813C8.21246 8.65781 5.88184 10.4653 5.85653 10.485C5.78013 10.5502 5.6914 10.5995 5.59561 10.6297C5.49982 10.66 5.39893 10.6708 5.29891 10.6613C5.19889 10.6519 5.1018 10.6224 5.01338 10.5747C4.92497 10.527 4.84703 10.462 4.78421 10.3836C4.7214 10.3052 4.67497 10.215 4.64769 10.1183C4.6204 10.0216 4.61282 9.92045 4.6254 9.82077C4.63797 9.7211 4.67044 9.62497 4.72088 9.53809C4.77132 9.45121 4.8387 9.37534 4.91903 9.315C5.05965 9.2025 8.4084 6.59625 13.3106 9.93094C17.5734 12.8288 20.054 11.835 20.0775 11.8238C20.1673 11.7813 20.2647 11.7572 20.364 11.7527C20.4632 11.7482 20.5624 11.7635 20.6557 11.7977C20.749 11.8319 20.8346 11.8843 20.9075 11.9518C20.9804 12.0193 21.0391 12.1007 21.0803 12.1911C21.1215 12.2816 21.1443 12.3793 21.1474 12.4786C21.1505 12.5779 21.1338 12.6769 21.0983 12.7697C21.0628 12.8625 21.0093 12.9473 20.9407 13.0193C20.8722 13.0912 20.79 13.1488 20.699 13.1888H20.6981Z"
        fill="white"
      />
    </g>
  </svg>
);

const FoodAppIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="clamp(20px, 4vw, 25px)"
    height="clamp(20px, 4vw, 24px)"
    viewBox="0 0 25 24"
    fill="none">
    <g>
      <path
        d="M4.6406 9.75H19.6275C19.852 9.75114 20.0739 9.70186 20.2769 9.60579C20.4798 9.50973 20.6586 9.36933 20.8 9.19496C20.9415 9.02058 21.042 8.81668 21.0941 8.59829C21.1462 8.37989 21.1487 8.15259 21.1012 7.93313C20.4047 4.64063 16.634 2.25 12.134 2.25C7.63403 2.25 3.86341 4.64063 3.16685 7.93313C3.11941 8.15259 3.12185 8.37989 3.17398 8.59829C3.22611 8.81668 3.3266 9.02058 3.46805 9.19496C3.60949 9.36933 3.78828 9.50973 3.99121 9.60579C4.19415 9.70186 4.41607 9.75114 4.6406 9.75ZM12.134 3.75C15.8672 3.75 19.0912 5.68219 19.6275 8.25H4.6406L4.63403 8.24344C5.17685 5.68219 8.40091 3.75 12.134 3.75ZM21.6272 14.295L17.7712 15.7013L14.2903 14.3034C14.1115 14.232 13.9122 14.232 13.7334 14.3034L10.2647 15.6919L6.79028 14.3034C6.61924 14.2351 6.42903 14.2321 6.25591 14.295L2.13091 15.795C1.95657 15.8717 1.81796 16.0119 1.74321 16.1871C1.66847 16.3624 1.66319 16.5594 1.72845 16.7384C1.79371 16.9173 1.92462 17.0648 2.0946 17.1507C2.26458 17.2367 2.46091 17.2548 2.64372 17.2013L3.88403 16.7522V17.25C3.88403 18.2446 4.27912 19.1984 4.98238 19.9016C5.68564 20.6049 6.63947 21 7.63403 21H16.634C17.6286 21 18.5824 20.6049 19.2857 19.9016C19.9889 19.1984 20.384 18.2446 20.384 17.25V16.3434L22.14 15.705C22.2384 15.6762 22.3299 15.6275 22.4088 15.5619C22.4877 15.4963 22.5523 15.4152 22.5986 15.3237C22.6449 15.2321 22.6719 15.132 22.678 15.0296C22.6841 14.9272 22.6691 14.8246 22.6339 14.7282C22.5988 14.6319 22.5442 14.5437 22.4736 14.4693C22.4031 14.3948 22.318 14.3356 22.2236 14.2953C22.1292 14.2551 22.0276 14.2346 21.925 14.2352C21.8224 14.2358 21.7211 14.2574 21.6272 14.2987V14.295ZM18.884 17.25C18.884 17.8467 18.647 18.419 18.225 18.841C17.8031 19.2629 17.2308 19.5 16.634 19.5H7.63403C7.0373 19.5 6.465 19.2629 6.04304 18.841C5.62109 18.419 5.38403 17.8467 5.38403 17.25V16.2075L6.49685 15.8025L9.97778 17.1966C10.1565 17.268 10.3559 17.268 10.5347 17.1966L14.0034 15.8081L17.4722 17.1966C17.6432 17.2649 17.8334 17.2679 18.0065 17.205L18.8756 16.8891L18.884 17.25ZM1.63403 12C1.63403 11.8011 1.71305 11.6103 1.8537 11.4697C1.99436 11.329 2.18512 11.25 2.38403 11.25H21.884C22.0829 11.25 22.2737 11.329 22.4144 11.4697C22.555 11.6103 22.634 11.8011 22.634 12C22.634 12.1989 22.555 12.3897 22.4144 12.5303C22.2737 12.671 22.0829 12.75 21.884 12.75H2.38403C2.18512 12.75 1.99436 12.671 1.8537 12.5303C1.71305 12.3897 1.63403 12.1989 1.63403 12Z"
        fill="white"
      />
    </g>
  </svg>
);

const WeatherAppIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="clamp(20px, 4vw, 25px)"
    height="clamp(20px, 4vw, 24px)"
    viewBox="0 0 25 24"
    fill="none">
    <g>
      <path
        d="M16.2559 6.74982C15.8678 6.74964 15.4803 6.781 15.0972 6.84357C14.9609 5.43578 14.3855 4.10647 13.4523 3.04369C12.519 1.98091 11.2752 1.23846 9.89688 0.921386C9.77309 0.892947 9.6441 0.896411 9.52202 0.931454C9.39994 0.966498 9.28875 1.03197 9.19889 1.12174C9.10904 1.2115 9.04345 1.32262 9.00828 1.44467C8.9731 1.56671 8.96951 1.69569 8.99782 1.81951C9.17447 2.58907 9.17542 3.38855 9.00061 4.15853C8.8258 4.92851 8.47971 5.6492 7.98807 6.26703C7.49644 6.88487 6.87188 7.38397 6.16083 7.72724C5.44978 8.07051 4.67051 8.24913 3.88094 8.24982C3.48375 8.24988 3.08782 8.20522 2.70063 8.1167C2.57674 8.0882 2.44765 8.09165 2.32547 8.12674C2.20329 8.16182 2.09203 8.22739 2.00214 8.31727C1.91225 8.40716 1.84669 8.51842 1.8116 8.6406C1.77652 8.76278 1.77306 8.89188 1.80156 9.01576C2.00305 9.88457 2.3742 10.705 2.8937 11.43C3.4132 12.1549 4.07081 12.7701 4.82875 13.2401C4.29569 13.9663 3.97428 14.8261 3.90018 15.7239C3.82609 16.6216 4.00221 17.5224 4.409 18.3262C4.81579 19.13 5.43734 19.8053 6.20467 20.2773C6.972 20.7492 7.85509 20.9993 8.75594 20.9998H16.2559C18.1456 20.9998 19.9579 20.2492 21.2941 18.913C22.6303 17.5768 23.3809 15.7645 23.3809 13.8748C23.3809 11.9852 22.6303 10.1729 21.2941 8.83669C19.9579 7.50049 18.1456 6.74982 16.2559 6.74982ZM3.63438 9.74982C3.71594 9.74982 3.79844 9.74982 3.88094 9.74982C5.67054 9.74784 7.38628 9.03604 8.65172 7.7706C9.91716 6.50516 10.629 4.78943 10.6309 2.99982C10.6309 2.91639 10.6309 2.83295 10.6309 2.74951C11.4946 3.15367 12.2309 3.78686 12.7598 4.58025C13.2888 5.37364 13.59 6.29685 13.6309 7.24951C12.7149 7.61328 11.8839 8.16249 11.1902 8.86267C10.4965 9.56285 9.95496 10.3989 9.59969 11.3183C8.96559 11.2085 8.31605 11.2254 7.68852 11.368C7.06099 11.5106 6.46789 11.776 5.94344 12.1489C4.93473 11.6197 4.1246 10.778 3.63438 9.74982ZM16.2559 19.4998H8.75594C8.29434 19.499 7.83782 19.4034 7.4146 19.2191C6.99139 19.0348 6.61049 18.7657 6.29544 18.4283C5.98039 18.0909 5.7379 17.6925 5.58296 17.2577C5.42801 16.8229 5.36392 16.3609 5.39463 15.9003C5.42534 15.4397 5.5502 14.9903 5.7615 14.5799C5.9728 14.1695 6.26604 13.8068 6.62309 13.5143C6.98013 13.2217 7.3934 13.0055 7.83734 12.879C8.28127 12.7525 8.74644 12.7184 9.20407 12.7789C9.16938 13.0011 9.145 13.2279 9.13188 13.4567C9.12621 13.5552 9.14001 13.6538 9.17247 13.747C9.20492 13.8402 9.25541 13.926 9.32105 13.9997C9.38669 14.0733 9.4662 14.1333 9.55502 14.1762C9.64385 14.2192 9.74026 14.2442 9.83875 14.2498C9.93724 14.2555 10.0359 14.2417 10.129 14.2092C10.2222 14.1768 10.3081 14.1263 10.3817 14.0606C10.4554 13.995 10.5154 13.9155 10.5583 13.8267C10.6012 13.7379 10.6262 13.6414 10.6319 13.5429C10.6529 13.1689 10.712 12.7979 10.8081 12.4358C10.8081 12.4208 10.8166 12.4058 10.8194 12.3908C11.095 11.3777 11.6484 10.462 12.4171 9.74684C13.1857 9.03172 14.139 8.54573 15.1692 8.34375C16.1994 8.14178 17.2656 8.23186 18.2474 8.60383C19.2291 8.9758 20.0874 9.61484 20.7251 10.4488C21.3629 11.2827 21.7548 12.2784 21.8565 13.3233C21.9583 14.3682 21.7659 15.4208 21.3011 16.3621C20.8363 17.3035 20.1176 18.0961 19.2261 18.6506C18.3346 19.205 17.3058 19.4992 16.2559 19.4998Z"
        fill="white"
      />
    </g>
  </svg>
);

const CalendarAppIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="clamp(20px, 4vw, 25px)"
    height="clamp(20px, 4vw, 24px)"
    viewBox="0 0 25 24"
    fill="none">
    <g>
      <path
        d="M19.634 3H17.384V2.25C17.384 2.05109 17.305 1.86032 17.1644 1.71967C17.0237 1.57902 16.8329 1.5 16.634 1.5C16.4351 1.5 16.2444 1.57902 16.1037 1.71967C15.963 1.86032 15.884 2.05109 15.884 2.25V3H8.38403V2.25C8.38403 2.05109 8.30502 1.86032 8.16436 1.71967C8.02371 1.57902 7.83295 1.5 7.63403 1.5C7.43512 1.5 7.24436 1.57902 7.1037 1.71967C6.96305 1.86032 6.88403 2.05109 6.88403 2.25V3H4.63403C4.23621 3 3.85468 3.15804 3.57337 3.43934C3.29207 3.72064 3.13403 4.10218 3.13403 4.5V19.5C3.13403 19.8978 3.29207 20.2794 3.57337 20.5607C3.85468 20.842 4.23621 21 4.63403 21H19.634C20.0319 21 20.4134 20.842 20.6947 20.5607C20.976 20.2794 21.134 19.8978 21.134 19.5V4.5C21.134 4.10218 20.976 3.72064 20.6947 3.43934C20.4134 3.15804 20.0319 3 19.634 3ZM6.88403 4.5V5.25C6.88403 5.44891 6.96305 5.63968 7.1037 5.78033C7.24436 5.92098 7.43512 6 7.63403 6C7.83295 6 8.02371 5.92098 8.16436 5.78033C8.30502 5.63968 8.38403 5.44891 8.38403 5.25V4.5H15.884V5.25C15.884 5.44891 15.963 5.63968 16.1037 5.78033C16.2444 5.92098 16.4351 6 16.634 6C16.8329 6 17.0237 5.92098 17.1644 5.78033C17.305 5.63968 17.384 5.44891 17.384 5.25V4.5H19.634V7.5H4.63403V4.5H6.88403ZM19.634 19.5H4.63403V9H19.634V19.5ZM13.259 12.375C13.259 12.5975 13.1931 12.815 13.0694 13C12.9458 13.185 12.7701 13.3292 12.5646 13.4144C12.359 13.4995 12.1328 13.5218 11.9146 13.4784C11.6963 13.435 11.4959 13.3278 11.3385 13.1705C11.1812 13.0132 11.0741 12.8127 11.0306 12.5945C10.9872 12.3762 11.0095 12.15 11.0947 11.9445C11.1798 11.7389 11.324 11.5632 11.509 11.4396C11.694 11.316 11.9115 11.25 12.134 11.25C12.4324 11.25 12.7185 11.3685 12.9295 11.5795C13.1405 11.7905 13.259 12.0766 13.259 12.375ZM17.384 12.375C17.384 12.5975 17.3181 12.815 17.1944 13C17.0708 13.185 16.8951 13.3292 16.6896 13.4144C16.484 13.4995 16.2578 13.5218 16.0396 13.4784C15.8213 13.435 15.6209 13.3278 15.4635 13.1705C15.3062 13.0132 15.1991 12.8127 15.1556 12.5945C15.1122 12.3762 15.1345 12.15 15.2197 11.9445C15.3048 11.7389 15.449 11.5632 15.634 11.4396C15.819 11.316 16.0365 11.25 16.259 11.25C16.5574 11.25 16.8435 11.3685 17.0545 11.5795C17.2655 11.7905 17.384 12.0766 17.384 12.375ZM9.13403 16.125C9.13403 16.3475 9.06805 16.565 8.94444 16.75C8.82082 16.935 8.64512 17.0792 8.43955 17.1644C8.23399 17.2495 8.00778 17.2718 7.78956 17.2284C7.57133 17.185 7.37087 17.0778 7.21354 16.9205C7.0562 16.7632 6.94906 16.5627 6.90565 16.3445C6.86224 16.1262 6.88452 15.9 6.96967 15.6945C7.05482 15.4889 7.19901 15.3132 7.38402 15.1896C7.56902 15.066 7.78653 15 8.00903 15C8.3074 15 8.59355 15.1185 8.80453 15.3295C9.01551 15.5405 9.13403 15.8266 9.13403 16.125ZM13.259 16.125C13.259 16.3475 13.1931 16.565 13.0694 16.75C12.9458 16.935 12.7701 17.0792 12.5646 17.1644C12.359 17.2495 12.1328 17.2718 11.9146 17.2284C11.6963 17.185 11.4959 17.0778 11.3385 16.9205C11.1812 16.7632 11.0741 16.5627 11.0306 16.3445C10.9872 16.1262 11.0095 15.9 11.0947 15.6945C11.1798 15.4889 11.324 15.3132 11.509 15.1896C11.694 15.066 11.9115 15 12.134 15C12.4324 15 12.7185 15.1185 12.9295 15.3295C13.1405 15.5405 13.259 15.8266 13.259 16.125ZM17.384 16.125C17.384 16.3475 17.3181 16.565 17.1944 16.75C17.0708 16.935 16.8951 17.0792 16.6896 17.1644C16.484 17.2495 16.2578 17.2718 16.0396 17.2284C15.8213 17.185 15.6209 17.0778 15.4635 16.9205C15.3062 16.7632 15.1991 16.5627 15.1556 16.3445C15.1122 16.1262 15.1345 15.9 15.2197 15.6945C15.3048 15.4889 15.449 15.3132 15.634 15.1896C15.819 15.066 16.0365 15 16.259 15C16.5574 15 16.8435 15.1185 17.0545 15.3295C17.2655 15.5405 17.384 15.8266 17.384 16.125Z"
        fill="white"
      />
    </g>
  </svg>
);

function getSuggestionIcon(name: string) {
  switch (name) {
    case 'Health App':
      return <HealthAppIcon />;
    case 'Food Recipe App':
      return <FoodAppIcon />;
    case 'Weather App':
      return <WeatherAppIcon />;
    case 'Calendar App':
      return <CalendarAppIcon />;
    default:
      return null;
  }
}

const SuggestionList: React.FC<SuggestionListProps> = ({onSuggestionClick}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  return (
    <div style={styles.grid}>
      {suggestions.map((suggestion, index) => (
        <div
          style={{
            ...styles.suggestionItem,
            ...(hoveredIndex === index && styles.suggestionItemHovered),
          }}
          key={index}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(null)}
          onClick={() => {
            Analytics.track('tile_suggestion_clicked', {
              suggestion: suggestion.name,
            });
            onSuggestionClick(suggestion.prompt);
          }}>
          {getSuggestionIcon(suggestion.name)}
          <p style={styles.suggestionText}>{suggestion.name}</p>
        </div>
      ))}
    </div>
  );
};

const styles = StyleSheet.create({
  grid: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    maxWidth:'clamp(600px, 60vw, 900px)',
    gap: 'clamp(8px, 2vw, 12px)',
    marginTop: 'clamp(10px, 3vw, 17px)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  suggestionItem: {
    // backgroundColor:'transparent',
    borderRadius: 80,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: 'rgba(51, 118, 225, 0.5)',
    // flex: 1,
    cursor: 'pointer',
    margin: '0px',
    paddingLeft: '15px',
    paddingRight: '15px',
    paddingTop: '6px',
    paddingBottom: '6px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
    border: '1px solid rgba(51, 118, 225, 0.5)',
    backgroundColor: 'rgba(0, 0, 0, 0.30)',
    backdropFilter: 'blur(5px)',
    transition: 'background-color 0.2s, transform 0.2s',
  },
  loaderImage: {
    width: 70,
    height: 70,
  },
  suggestionHovered: {
    backgroundColor: '#1A1A1A',
    borderColor: '#404040',
    transform: [{scale: 1.01}],
  },
  suggestionPressed: {
    backgroundColor: '#0F0F0F',
    transform: [{scale: 0.99}],
  },
  suggestionText: {
    color: '#FAFAFA',
    fontSize: 'clamp(10px, 2.5vw, 14px)',
    fontWeight: '400',
    padding: '0px',
    margin: '0px',
    fontFamily: 'General Sans',
    marginLeft: 'clamp(5px, 2vw, 10px)',
  },
  suggestionItemHovered: {
    backgroundColor: 'rgba(176, 192, 239, 0.1)',
    border: '1px solid rgb(87, 142, 224)',
    transform: 'scale(1.03)',
  },
});

export default SuggestionList;
