import React, {useState} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import VoucherApi from '@/root/web/api/VoucherApi';
import { useParams } from 'react-router-dom';
import { makeToast } from '@/root/web/actions/toastActions';

interface IRedeemVoucherModal {
  onClose: () => void;
  onRedeem: () => void;
}

const ErrorIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 14.6667C11.6819 14.6667 14.6667 11.6819 14.6667 8C14.6667 4.3181 11.6819 1.33333 8 1.33333C4.3181 1.33333 1.33333 4.3181 1.33333 8C1.33333 11.6819 4.3181 14.6667 8 14.6667Z" stroke="#FF3B30" strokeWidth="1.2"/>
    <path d="M8 5.33333V8.66667" stroke="#FF3B30" strokeWidth="1.2" strokeLinecap="round"/>
    <circle cx="8" cy="11" r="0.666667" fill="#FF3B30"/>
  </svg>
);

const RedeemVoucherModal: React.FC<IRedeemVoucherModal> = ({onClose, onRedeem}) => {
  const params = useParams();
  const dispatch = useDispatch();
  const [voucherCode, setVoucherCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const userEmail = useSelector((state: any) => state.user?.user?.email);
  const organizationId = params.orgId;

  const handleRedeem = async () => {
    setError('');
    
    if (!voucherCode.trim()) {
      setError('Please enter a voucher code');
      return;
    }

    if (!userEmail || !organizationId) {
      setError('User information not found. Please try logging in again.');
      return;
    }

    setIsLoading(true);

    try {
      const response = await VoucherApi.redeemVoucher(voucherCode, userEmail, organizationId);
      dispatch(makeToast({content: 'Voucher redeemed', appearances: 'success'}));
      onRedeem();
      onClose();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to redeem voucher. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div style={styles.modalContainer}>
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'flex-end',
            borderRadius: '50px',
            height: 'fit-content',
          }}>
          <div onClick={onClose} style={styles.closeButton}>
            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
              <path d="M2 2L13 13" stroke="white" stroke-width="2" stroke-linecap="round" />
              <path d="M13 2L2 13" stroke="white" stroke-width="2" stroke-linecap="round" />
            </svg>
          </div>
        </div>

        <p style={styles.title}>{'Redeem Voucher'}</p>
        
        <div style={styles.inputContainer}>
          <input
            type="text"
            value={voucherCode}
            onChange={(e) => {
              setVoucherCode(e.target.value);
              setError('');
            }}
            placeholder="Enter voucher code"
            style={{
              ...styles.input,
              borderColor: error ? '#FF3B30' : '#828282',
            }}
          />
          {error && (
            <div style={styles.errorContainer}>
              <ErrorIcon />
              <p style={styles.errorText}>{error}</p>
            </div>
          )}
          <button 
            onClick={handleRedeem} 
            disabled={isLoading}
            style={styles.redeemButton}
          >
            {isLoading ? 'Redeeming...' : 'Redeem'}
          </button>
        </div>
      </div>
    </>
  );
};

const styles = {
  closeButton: {
    marginTop: 22,
    marginRight: 24,
    cursor: 'pointer',
  },
  title: {
    color: '#EFEFEF',
    fontFamily: 'General Sans',
    fontSize: 20,
    fontWeight: 500,
    margin: '0px',
    marginTop: '12px',
  },
  signUpText: {
    color: '#FFF',
    fontFamily: 'General Sans',
    fontSize: 13,
    fontWeight: 400,
    marginTop: '10px',
  },
  signUpTextLink: {
    color: '#FFF',
    fontFamily: 'General Sans',
    fontSize: 13,
    fontWeight: 600,
    textDecoration: 'underline',
  },
  modalContainer: {
    borderRadius: 20,
    border: '1px solid #828282',
    backgroundColor: '#1A1A1A',
    borderColor: '#828282',
    width: 500,
    height: 335,
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
  },
  inputContainer: {
    width: '50%',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    marginTop: 40,
  },
  input: {
    width: '250px',
    height: 40,
    backgroundColor: '#2A2A2A',
    border: '1px solid #828282',
    borderRadius: 8,
    padding: '0 15px',
    color: '#FFFFFF',
    fontSize: 14,
    outline: 'none',
    margin: 0,
    transition: 'border-color 0.2s ease',
  },
  redeemButton: {
    width: '280px',
    height: 40,
    backgroundColor: '#007AFF',
    border: 'none',
    borderRadius: 8,
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 500,
    cursor: 'pointer',
    margin: 0,
    marginTop: 20,
    ':disabled': {
      opacity: 0.7,
      cursor: 'not-allowed',
    },
  },
  errorContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    width: '280px',
    marginTop: 8,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 13,
    fontFamily: 'General Sans',
    margin: 0,
    flex: 1,
  },
} as const;

export default RedeemVoucherModal;
