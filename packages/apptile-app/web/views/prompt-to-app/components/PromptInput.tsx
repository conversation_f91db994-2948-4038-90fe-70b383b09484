import React, {useState, useCallback, useEffect, useMemo, useRef} from 'react';
import {StyleSheet, TextInput, View, Animated, Pressable} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {useNavigate, useParams} from '@/root/web/routing.web';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {selectWorkspaces} from '@/root/web/selectors/WorkspaceSelector';

import {
  createOrgApp,
  verifyAppForks,
  fetchOrgs,
  setLandingPagePrompt,
  openChatView,
} from '@/root/web/actions/editorActions';
import AppConfigApi from '@/root/web/api/AppConfigApi';
import {cloneOpenApp} from '@/root/web/components/pluginServer';
import LoadingOverlay from './LoadingOverlay';
import AppNameGeneratorApi from '@/root/web/api/AppNameGeneratorApi';
import OrgApi from '@/root/web/api/OrgApi';
import {makeToast} from '@/root/web/actions/toastActions';
import Analytics from '@/root/web/lib/segment';

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit?: () => void; // Add this new prop
}

interface IImageItem {
  src: string;
  loading: boolean;
}

const PromptInput: React.FC<PromptInputProps> = ({value, onChange, onSubmit}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  let params = useParams();

  const isLoggedIn = useSelector((state: EditorRootState) => state.user.userLoggedIn);
  const workspaces = useSelector(selectWorkspaces);
  // Use a hardcoded default blueprint ID instead of trying to fetch blueprints
  const [isCreatingApp, setIsCreatingApp] = useState(false);
  const [isCloning, setIsCloning] = useState(false);
  const [cloningError, setCloningError] = useState('');
  const [createdAppName, setCreatedAppName] = useState('');
  const [createdOrgId, setCreatedOrgId] = useState('');
  const [promptText, setPromptText] = useState(''); // without img tag
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);
  // We'll store the current app ID for which we're fetching forks
  const [_currentAppIdForForks, setCurrentAppIdForForks] = useState('');
  const MAX_HEIGHT = 200; // Maximum height in pixels

  // Ref for triggering app creation programmatically
  const createAppRef = useRef<(() => void) | null>(null);

  const [images, setImages] = useState<IImageItem[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isDragActive, setIsDragActive] = useState(false);

  const [animatedPlaceholder, setAnimatedPlaceholder] = useState('');
  const [isUserActive, setIsUserActive] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setPromptText(value);
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const newHeight = Math.min(Math.max(textareaRef.current.scrollHeight, 70), 180);
      textareaRef.current.style.height = newHeight + "px";
    }
    if (images.length > 0) {
      onChange(value + ' <img src="' + images[0].src + '">');
    } else {
      onChange(value);
    }
    setIsUserActive(true);
  };
  const examplePrompts = useMemo(
    () => ['Build a health app', 'Build a food Recipe App', 'Build a weather App', 'Build a calendar App'],
    [],
  );
  const prefix = 'Build a ';
  const typingSpeed = 60;
  const deletingSpeed = 40;
  const pauseAfterTyped = 1200;
  const pauseAfterDeleted = 500;

  useEffect(() => {
    if (isUserActive) return;
    let isMounted = true;
    let promptIdx = 0;
    let charIdx = 0;
    let isDeleting = false;
    let timeoutId: NodeJS.Timeout;

    const getSuffix = (prompt: string) => prompt.replace(prefix, '');

    const animate = () => {
      if (!isMounted) return;
      const prompt = examplePrompts[promptIdx];
      const suffix = getSuffix(prompt);
      const fullText = prefix + suffix;
      if (!isDeleting) {
        if (charIdx <= fullText.length) {
          setAnimatedPlaceholder(fullText.slice(0, charIdx));
          charIdx++;
          timeoutId = setTimeout(animate, typingSpeed);
        } else {
          timeoutId = setTimeout(() => {
            isDeleting = true;
            animate();
          }, pauseAfterTyped);
        }
      } else {
        if (charIdx > prefix.length) {
          setAnimatedPlaceholder(fullText.slice(0, charIdx - 1));
          charIdx--;
          timeoutId = setTimeout(animate, deletingSpeed);
        } else {
          // Pause, then move to next prompt
          timeoutId = setTimeout(() => {
            isDeleting = false;
            promptIdx = (promptIdx + 1) % examplePrompts.length;
            charIdx = prefix.length + 1;
            animate();
          }, pauseAfterDeleted);
        }
      }
    };
    charIdx = prefix.length + 1;
    setAnimatedPlaceholder(prefix);
    animate();
    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [isUserActive, examplePrompts]);

  // Add this effect to fetch workspaces when login status changes
  useEffect(() => {
    if (isLoggedIn) {
      // Fetch organizations/workspaces when user logs in
      dispatch(fetchOrgs());
    }
  }, [isLoggedIn, dispatch]);

  // Handle logged-out user prompt restoration after login
  useEffect(() => {
    if (isLoggedIn) {
      try {
        const storedPrompt = localStorage.getItem('loggedOutUserPrompt');
        if (storedPrompt && storedPrompt.trim()) {
          console.log('Found stored logged-out user prompt:', storedPrompt);

          // Set the prompt in the input
          onChange(storedPrompt);
          setPromptText(storedPrompt);

          // Immediately clean up the stored prompt
          localStorage.removeItem('loggedOutUserPrompt');

          // Small delay to ensure the UI is updated, then trigger app creation
          setTimeout(() => {
            if (createAppRef.current) {
              createAppRef.current();
            }
          }, 500);
        }
      } catch (error) {
        console.error('Error handling logged-out user prompt:', error);
        // Clean up on error to prevent stale data
        try {
          localStorage.removeItem('loggedOutUserPrompt');
        } catch (cleanupError) {
          console.error('Error cleaning up logged-out user prompt:', cleanupError);
        }
      }
    }
  }, [isLoggedIn, onChange]);

  // Modify createNewApp to handle the flow better
  // Fallback function for random name generation
  const generateRandomAppName = () => {
    const adjectives = ['Amazing', 'Brilliant', 'Creative', 'Dynamic', 'Elegant', 'Fantastic', 'Gorgeous'];
    const nouns = ['App', 'Solution', 'Platform', 'Project', 'System', 'Tool', 'Service'];
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    return `${randomAdjective} ${randomNoun} ${Math.floor(Math.random() * 1000)}`;
  };

  // Generate an app name using ChatGPT API or fallback to random generation
  const generateAppName = async () => {
    // If no prompt value, fall back to random name
    if (!value.trim()) {
      return generateRandomAppName();
    }

    // Try to get a name from backend with exponential backoff
    let retryCount = 0;
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    while (retryCount < maxRetries) {
      try {
        // Use the AppNameGeneratorApi class
        const response = await AppNameGeneratorApi.generateAppName(promptText);

        if (!response || !response.data) {
          throw new Error('Backend API returned empty response');
        }

        const appName = response.data.appName;

        // Verify we got a sensible name (not empty, not too long)
        if (appName && appName.length > 0 && appName.split(' ').length <= 3) {
          return appName;
        } else {
          throw new Error('Generated name was not suitable');
        }
      } catch (error) {
        console.error(`Backend API attempt ${retryCount + 1} failed:`, error);
        retryCount++;

        // If not the last retry, wait before trying again
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, baseDelay * Math.pow(2, retryCount - 1)));
        }
      }
    }

    // If all attempts failed, fall back to random name generation
    console.log('All backend API attempts failed, using random app name');
    return generateRandomAppName();
  };

  let newAppId = '';

  const createNewApp = useCallback(async () => {
    if (!value.trim()) {
      return; // Don't submit empty input
    }
    console.log('Creating new app, isLoggedIn:', isLoggedIn);

    if (!isLoggedIn && onSubmit) {
      // Store the prompt in localStorage before showing the login modal
      try {
        localStorage.setItem('loggedOutUserPrompt', value);
        console.log('Stored logged-out user prompt in localStorage:', value);
      } catch (error) {
        console.error('Error storing logged-out user prompt:', error);
        // Continue with login flow even if storage fails
      }
      console.log('User not logged in, triggering onSubmit');
      Analytics.track('app_prompt_submitted');
      onSubmit();
      return;
    }

    if (!workspaces?.length) {
      console.log('No workspaces available');
      return;
    }

    if (isCreatingApp) {
      console.log('Already creating app');
      return;
    }

    if (isLoggedIn && workspaces && workspaces.length > 0 && !isCreatingApp) {
      setIsCreatingApp(true);

      const selectedOrgId = params?.orgId;
      if (!selectedOrgId) {
        setIsCreatingApp(false);
        return;
      }
      console.log(`Selected organization: ID: ${selectedOrgId}`);
      // Use the provided default blueprint ID
      const baseBlueprintId = '377cc13c-add3-4fb7-8c88-9ce2368d1c3f'; //Blank Ai theme

      if (baseBlueprintId) {
        // Create a new app with a name generated from ChatGPT or fallback
        const appName = await generateAppName();

        // Save the app name and org ID to use later
        setCreatedAppName(appName);
        setCreatedOrgId(selectedOrgId);

        // Dispatch action to create the app
        const appCreationResp = await OrgApi.createOrgsApps(selectedOrgId, appName, baseBlueprintId);
        newAppId = appCreationResp?.data?.uuid;
        console.log('App created successfully:', appCreationResp.data);
        redirectAppTo(newAppId, selectedOrgId);
        // We'll navigate in the useEffect that watches for the new app
      } else {
        // If no blueprint is available, just navigate to the dashboard
        navigate(`/dashboard/${selectedOrgId}`);
        setIsCreatingApp(false);
      }
    } else if (!isLoggedIn) {
      // If not logged in, redirect to login page
      navigate('/login');
    }
  }, [isLoggedIn, workspaces, isCreatingApp, dispatch, navigate, onSubmit]);

  // Set up the ref for programmatic triggering
  useEffect(() => {
    createAppRef.current = createNewApp;
  }, [createNewApp]);

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // Prevent default newline
      console.log('Enter key pressed, isLoggedIn:', isLoggedIn);

      if (!value.trim()) {
        return; // Don't submit empty input
      }

      if (!isLoggedIn && onSubmit) {
        // Store the prompt in localStorage before showing the login modal
        try {
          localStorage.setItem('loggedOutUserPrompt', value);
          console.log('Stored logged-out user prompt in localStorage:', value);
        } catch (error) {
          console.error('Error storing logged-out user prompt:', error);
          // Continue with login flow even if storage fails
        }
        onSubmit(); // Call onSubmit for non-logged in users
      } else {
        createNewApp(); // Use existing functionality for logged in users
      }
    }
  };

  const redirectAppTo = (newAppId: string, createdOrgId: string) => {
    // Return the current app ID for which we're fetching forks
    if (newAppId) {
      // Save the app ID for which we're fetching forks
      setCurrentAppIdForForks(newAppId);

      // Verify app forks - this will create a fork if none exists
      dispatch(verifyAppForks(newAppId));

      // Start polling for the app manifest which contains the fork ID
      const pollInterval = 5000; // 5 seconds
      const maxAttempts = 15; // 75 seconds total
      let attempts = 0;

      const pollForManifest = async () => {
        try {
          // Fetch the app manifest which contains the fork ID
          const manifest: any = await AppConfigApi.fetchAppManifest(newAppId);
          console.log('App manifest:', manifest);

          // Check if the manifest has forks
          if (manifest && manifest.forks && manifest.forks.length > 0) {
            // We have a fork! Get the first fork ID
            // const forkId = manifest.forks[0].id;

            // Start the cloning process before navigation
            setIsCloning(true);
            setCloningError('');

            try {
              // Call cloneOpenApp and wait for it to complete
              const cloneResult = await cloneOpenApp(newAppId);

              if (cloneResult.success) {
                console.log('App cloned successfully:', cloneResult.data);
                dispatch(openChatView());
                dispatch(setLandingPagePrompt(value));
                // Store the initial prompt in sessionStorage
                // sessionStorage.setItem('initialChatPrompt', value);
                // console.log('Stored initial prompt in sessionStorage:', value);

                // Navigate to the studio page with the default branch 'main'
                // Add query parameter to indicate that the chat should be opened
                navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
              } else {
                // Cloning failed, but we'll still navigate
                console.error('Cloning failed:', cloneResult.error);
                setCloningError(cloneResult.error || 'Failed to clone app');
                dispatch(openChatView());
                dispatch(setLandingPagePrompt(value));
                // Store the initial prompt in sessionStorage
                // sessionStorage.setItem('initialChatPrompt', value);
                // console.log('Stored initial prompt in sessionStorage:', value);

                // Navigate anyway after a short delay
                setTimeout(() => {
                  navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
                }, 2000);
              }
            } catch (error) {
              // Unexpected error during cloning
              console.error('Unexpected error during cloning:', error);
              setCloningError('Unexpected error during cloning');
              dispatch(openChatView());
              dispatch(setLandingPagePrompt(value));
              // Store the initial prompt in sessionStorage
              // sessionStorage.setItem('initialChatPrompt', value);
              // console.log('Stored initial prompt in sessionStorage:', value);

              // Navigate anyway after a short delay
              setTimeout(() => {
                navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
              }, 2000);
            } finally {
              setIsCloning(false);
              setIsCreatingApp(false);
              setCreatedAppName('');
              setCreatedOrgId('');
              setCurrentAppIdForForks('');
            }

            return; // Exit the polling
          }
        } catch (error) {
          console.error('Error fetching app manifest:', error);
        }

        // Set up the next poll
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(pollForManifest, pollInterval);
        } else {
          console.error('Failed to get fork ID after maximum attempts');
          // Navigate to the app dashboard as fallback
          navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
          setIsCreatingApp(false);
          setCreatedAppName('');
          setCreatedOrgId('');
          setCurrentAppIdForForks('');
        }
      };

      // Start polling
      setTimeout(pollForManifest, pollInterval);

      // Don't reset state here - we'll reset it after navigation
    }
  };

  // We don't want to auto-create an app when a suggestion is clicked
  // This was causing multiple apps to be created simultaneously

  // Effect to navigate to the newly created app
  useEffect(() => {
    if (createdAppName && createdOrgId && isCreatingApp && Object.keys(appsById).length > 0) {
      // Find the newly created app by name
      // const newAppId = Object.keys(appsById).find(appId => {
      //   const app = appsById[appId];
      //   return app.name === createdAppName;
      // });

      if (newAppId) {
        // Save the app ID for which we're fetching forks
        setCurrentAppIdForForks(newAppId);

        // Verify app forks - this will create a fork if none exists
        dispatch(verifyAppForks(newAppId));

        // Start polling for the app manifest which contains the fork ID
        const pollInterval = 5000; // 5 seconds
        const maxAttempts = 15; // 75 seconds total
        let attempts = 0;

        const pollForManifest = async () => {
          try {
            // Fetch the app manifest which contains the fork ID
            const manifest: any = await AppConfigApi.fetchAppManifest(newAppId);
            console.log('App manifest:', manifest);

            // Check if the manifest has forks
            if (manifest && manifest.forks && manifest.forks.length > 0) {
              // We have a fork! Get the first fork ID
              const forkId = manifest.forks[0].id;

              // Start the cloning process before navigation
              setIsCloning(true);
              setCloningError('');

              try {
                // Call cloneOpenApp and wait for it to complete
                const cloneResult = await cloneOpenApp(newAppId);

                if (cloneResult.success) {
                  console.log('App cloned successfully:', cloneResult.data);
                  dispatch(openChatView());
                  dispatch(setLandingPagePrompt(value));
                  // Store the initial prompt in sessionStorage
                  // sessionStorage.setItem('initialChatPrompt', value);
                  // console.log('Stored initial prompt in sessionStorage:', value);

                  // Navigate to the studio page with the default branch 'main'
                  // Add query parameter to indicate that the chat should be opened
                  navigate(
                    `/dashboard/${createdOrgId}/app/${newAppId}/f/${forkId}/b/main/dashboard/editor?openChat=true`,
                  );
                } else {
                  // Cloning failed, but we'll still navigate
                  console.error('Cloning failed:', cloneResult.error);
                  setCloningError(cloneResult.error || 'Failed to clone app');
                  dispatch(openChatView());
                  dispatch(setLandingPagePrompt(value));
                  // Store the initial prompt in sessionStorage
                  // sessionStorage.setItem('initialChatPrompt', value);
                  // console.log('Stored initial prompt in sessionStorage:', value);

                  // Navigate anyway after a short delay
                  setTimeout(() => {
                    navigate(
                      `/dashboard/${createdOrgId}/app/${newAppId}/f/${forkId}/b/main/dashboard/editor?openChat=true`,
                    );
                  }, 2000);
                }
              } catch (error) {
                // Unexpected error during cloning
                console.error('Unexpected error during cloning:', error);
                setCloningError('Unexpected error during cloning');
                dispatch(openChatView());
                dispatch(setLandingPagePrompt(value));
                // Store the initial prompt in sessionStorage
                // sessionStorage.setItem('initialChatPrompt', value);
                // console.log('Stored initial prompt in sessionStorage:', value);

                // Navigate anyway after a short delay
                setTimeout(() => {
                  navigate(
                    `/dashboard/${createdOrgId}/app/${newAppId}/f/${forkId}/b/main/dashboard/editor?openChat=true`,
                  );
                }, 2000);
              } finally {
                // Reset state
                setIsCloning(false);
                setIsCreatingApp(false);
                setCreatedAppName('');
                setCreatedOrgId('');
                setCurrentAppIdForForks('');
              }

              return; // Exit the polling
            }
          } catch (error) {
            console.error('Error fetching app manifest:', error);
          }

          // Set up the next poll
          attempts++;
          if (attempts < maxAttempts) {
            setTimeout(pollForManifest, pollInterval);
          } else {
            console.error('Failed to get fork ID after maximum attempts');
            // Navigate to the app dashboard as fallback
            navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
            setIsCreatingApp(false);
            setCreatedAppName('');
            setCreatedOrgId('');
            setCurrentAppIdForForks('');
          }
        };

        // Start polling
        setTimeout(pollForManifest, pollInterval);

        // Don't reset state here - we'll reset it after navigation
      }
    }
  }, [appsById, createdAppName, createdOrgId, isCreatingApp, navigate, dispatch, newAppId]);

  // We don't need a separate effect to monitor forks anymore
  // The polling in createNewApp handles this

  // Create a pulsating animation for the glittering effect
  const pulseAnim = useMemo(() => {
    const anim = new Animated.Value(0.6);
    Animated.loop(
      Animated.sequence([
        Animated.timing(anim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(anim, {
          toValue: 0.6,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
    ).start();
    return anim;
  }, []);

  // Create a shimmer animation for the glittering effect
  const shimmerAnim = useMemo(() => {
    const anim = new Animated.Value(-100);
    Animated.loop(
      Animated.timing(anim, {
        toValue: 100,
        duration: 3000,
        useNativeDriver: true,
      }),
    ).start();
    return anim;
  }, []);

  // Create a rotation animation for the loading state
  const spinAnim = useMemo(() => {
    const anim = new Animated.Value(0);
    Animated.loop(
      Animated.timing(anim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
    ).start();
    return anim;
  }, []);

  // Interpolate the spin animation to rotate from 0 to 360 degrees
  const spin = spinAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // SVG Paper Plane icon for the send button
  const SendIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21.7 2.3C21.5 2.1 21.2 2 21 2C20.9 2 20.7 2 20.6 2.1L2.6 9.1C2.3 9.2 2.1 9.5 2 9.8C2 10.1 2.1 10.4 2.3 10.6L7 15.3L15.3 7L16.7 8.4L8.4 16.7L13.2 21.5C13.4 21.7 13.6 21.8 13.9 21.8C14 21.8 14.1 21.8 14.2 21.8C14.5 21.7 14.8 21.5 14.9 21.2L21.9 3.2C22 2.9 21.9 2.5 21.7 2.3Z"
        fill={themeColors.EDITOR_ACCENT_COLOR}
      />
    </svg>
  );

  // Loading spinner icon
  const LoadingIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z"
        fill={themeColors.EDITOR_ACCENT_COLOR + '40'}
      />
      <path d="M12 2V4C16.41 4 20 7.59 20 12H22C22 6.48 17.52 2 12 2Z" fill={themeColors.EDITOR_ACCENT_COLOR} />
    </svg>
  );

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (images.length === 0) {
      if (e.target.files && e.target.files.length === 1) {
        const file = e.target.files[0];
        const reader = new FileReader();
        reader.onload = ev => {
          const src = ev.target?.result as string;
          setImages([{src, loading: true}]);
          onChange(`${value} <img src="${src}">`);
          setTimeout(() => {
            setImages([{src, loading: false}]);
          }, 1500);
        };
        reader.readAsDataURL(file);
        e.target.value = '';
      } else {
        dispatch(
          makeToast({
            content: 'You can only upload one image',
            appearances: 'warning',
            duration: 3000,
          }),
        );
      }
    } else {
      dispatch(
        makeToast({
          content: 'You can only upload one image',
          appearances: 'warning',
          duration: 3000,
        }),
      );
    }
  };

  const handleRemove = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  // Handle drag and drop for image upload
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    if (images.length === 0) {
      const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
      if (files.length === 1) {
        const file = files[0];
        const reader = new FileReader();
        reader.onload = ev => {
          const src = ev.target?.result as string;
          setImages([{src, loading: true}]);
          setTimeout(() => {
            setImages([{src, loading: false}]);
            onChange(value + ' <img src="' + src + '">');
          }, 1500);
        };
        reader.readAsDataURL(file);
      } else if (files.length > 1) {
        dispatch(
          makeToast({
            content: 'You can only upload one image',
            appearances: 'warning',
            duration: 3000,
          }),
        );
      }
    } else {
      dispatch(
        makeToast({
          content: 'You can only upload one image',
          appearances: 'warning',
          duration: 3000,
        }),
      );
    }
  };

  useEffect(() => {
    if (!isUserActive) {
      const textOnly = value.replace(/<img[^>]*>/g, '').trim();
      setPromptText(textOnly);
    }
  }, [value, isUserActive]);

  return (
    <>
      <style>
        {`
          @keyframes pulses {
            0%   { transform: rotate(9deg); }
          
  100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div className="media-object">
        <div
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDragEnd={handleDragLeave}
          onDrop={handleDrop}
          style={{
            margin: '0px auto 0 auto',
            width: window.innerWidth <= 700 ? '98vw' : '726px',
            maxWidth: '95vw',
            boxSizing: 'border-box',
            position: 'relative',
            minHeight: '106px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-end',
            // padding: '1px',
            borderRadius: '16px',

            boxShadow: isDragActive ? '0 0 0 3px #007AFF55' : undefined,
            transition: 'background 0.2s, box-shadow 0.2s',
          }}>
          <div
            style={{
              background: '#121212',
              border: 'none',
              borderRadius: '16px',
              padding: 0,
              margin: 0,
              width: '100%',
              minHeight: '106px',
              boxSizing: 'border-box',
              position: 'relative',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-end',
            }}>
            {images.length > 0 && (
              <div style={{display: 'flex', flexWrap: 'wrap', gap: 12, margin: '13px 0 0 19px', padding: 0}}>
                {images.map((img, idx) => (
                  <div
                    key={idx}
                    className="prompt-image-preview-container"
                    style={{width: 46, height: 46, borderRadius: 12}}>
                    {img.loading ? (
                      <div
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          background: '#23262b',
                          borderRadius: 4,
                          border: '1px solid #333',
                        }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="12" cy="12" r="10" stroke="#888" strokeWidth="4" opacity="0.2" />
                          <path d="M12 2a10 10 0 0 1 10 10" stroke="#fff" strokeWidth="2" strokeLinecap="round">
                            <animateTransform
                              attributeName="transform"
                              type="rotate"
                              from="0 12 12"
                              to="360 12 12"
                              dur="1s"
                              repeatCount="indefinite"
                            />
                          </path>
                        </svg>
                      </div>
                    ) : (
                      <img
                        src={img.src}
                        alt={`Uploaded ${idx + 1}`}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          borderRadius: 4,
                          border: '1px solid #333',
                          background: '#23262b',
                          display: 'block',
                        }}
                      />
                    )}
                    <button onClick={() => handleRemove(idx)} className="prompt-remove-image-btn" aria-label="Remove">
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
            <div style={{position: 'relative', width: '100%'}}>
              {/* Custom Placeholder */}
              {!isUserActive && !promptText && (
                <div
                  style={{
                    position: 'absolute',
                    top: 13, // match your textarea's padding
                    left: 19,
                    pointerEvents: 'none',
                    color: '#aaa',
                    fontSize: '17px',
                    fontFamily: 'DM Sans',
                    fontWeight: 100,
                    lineHeight: 1.5,
                    zIndex: 1,
                    display: 'flex',
                  }}>
                  <span style={{color: '#A2A2A2'}}>Build a</span>
                  <span style={{color: '#62AEFA', marginLeft: 4}}>
                    {animatedPlaceholder.replace(/^Build a\s?/, '')}
                  </span>
                </div>
              )}

              {/* Textarea */}
              <textarea
                ref={textareaRef}
                value={promptText}
                onFocus={() => setIsUserActive(true)}
                onBlur={() => {
                  if (!promptText) setIsUserActive(false);
                }}
                onChange={handlePromptChange}
                onKeyPress={handleKeyPress}
                rows={1}
                style={{
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                  scrollbarWidth:'thin',
                  fontSize: 'clamp(8px, 2vw, 16px)',
                  resize: 'none',
                  margin: 0,
                  width: '100%',
                  lineHeight: 1.5,
                  fontFamily: 'General Sans',
                  fontWeight: 100,
                  padding: '13px 19px 13px 19px',
                  boxSizing: 'border-box',
                  overflowY: 'auto',
                  color: '#FFF',
                  height: '70px',
                  borderRadius: '12px',
                  position: 'relative',
                  zIndex: 2,
                }}
                placeholder="" // leave native placeholder empty
              />
            </div>
            <div
              style={{
                // position: 'absolute',
                right: '18px',
                bottom: '18px',
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                zIndex: 2,
                // width: '100%',
                justifyContent: `${images.length === 0 ? 'space-between' : 'flex-end'}`,
                margin: '0px 19px 13px 19px',
              }}>
              {images.length === 0 && (
                <button className="file-upload-plus-btn" onClick={() => fileInputRef.current?.click()} type="button">
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path
                      d="M18.7164 10.9994C18.7164 11.1699 18.6487 11.3335 18.5281 11.454C18.4076 11.5746 18.2441 11.6423 18.0736 11.6423H11.645V18.0709C11.645 18.2414 11.5773 18.4049 11.4567 18.5254C11.3361 18.646 11.1726 18.7137 11.0021 18.7137C10.8316 18.7137 10.6681 18.646 10.5476 18.5254C10.427 18.4049 10.3593 18.2414 10.3593 18.0709V11.6423H3.9307C3.7602 11.6423 3.59669 11.5746 3.47613 11.454C3.35557 11.3335 3.28784 11.1699 3.28784 10.9994C3.28784 10.8289 3.35557 10.6654 3.47613 10.5449C3.59669 10.4243 3.7602 10.3566 3.9307 10.3566H10.3593V3.92801C10.3593 3.75752 10.427 3.594 10.5476 3.47344C10.6681 3.35289 10.8316 3.28516 11.0021 3.28516C11.1726 3.28516 11.3361 3.35289 11.4567 3.47344C11.5773 3.594 11.645 3.75752 11.645 3.92801V10.3566H18.0736C18.2441 10.3566 18.4076 10.4243 18.5281 10.5449C18.6487 10.6654 18.7164 10.8289 18.7164 10.9994Z"
                      fill="#C4C4C4"
                    />
                  </svg>
                </button>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                style={{display: 'none'}}
                onChange={handleImageChange}
              />
              <button
                disabled={isCreatingApp || isCloning}
                onClick={createNewApp}
                className="send-upload-plus-btn"
                style={{
                  cursor: 'pointer',
                }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                  <rect x="0.000732422" width="32" height="32" rx="6" fill="#FAFAFA" />
                  <path
                    d="M16.0007 24C15.5626 24 15.207 23.6554 15.207 23.2308V10.5931L10.3632 15.1665C10.2893 15.2401 10.2009 15.2986 10.1032 15.3385C10.0056 15.3785 9.90064 15.3991 9.79464 15.3991C9.68864 15.3991 9.5837 15.3786 9.48603 15.3387C9.38836 15.2987 9.29993 15.2403 9.22596 15.1667C9.15199 15.0931 9.09397 15.0059 9.05533 14.9103C9.0167 14.8146 8.99823 14.7124 9.00101 14.6097C9.00379 14.507 9.02776 14.4059 9.07152 14.3124C9.11528 14.2188 9.17793 14.1347 9.25578 14.065L15.4451 8.22087C15.5932 8.07935 15.7928 8 16.0007 8C16.2087 8 16.4083 8.07935 16.5564 8.22087L22.7457 14.065C22.8235 14.1347 22.8862 14.2188 22.9299 14.3124C22.9737 14.4059 22.9977 14.507 23.0005 14.6097C23.0032 14.7124 22.9848 14.8146 22.9461 14.9103C22.9075 15.0059 22.8495 15.0931 22.7755 15.1667C22.7015 15.2403 22.6131 15.2987 22.5154 15.3387C22.4178 15.3786 22.3128 15.3991 22.2068 15.3991C22.1008 15.3991 21.9959 15.3785 21.8982 15.3385C21.8006 15.2986 21.7122 15.2401 21.6382 15.1665L16.7945 10.5931V23.2308C16.7945 23.6554 16.4389 24 16.0007 24Z"
                    fill="black"
                  />
                </svg>
              </button>
            </div>
          </div>

          <LoadingOverlay visible={isCreatingApp || isCloning} error={cloningError} />
        </div>
      </div>
    </>
  );
};

const containerStyle: React.CSSProperties = {
  position: 'relative',
  // padding: '2rem',
  borderRadius: 12,
  overflow: 'hidden',
  backgroundColor: '#0080FF',
  // boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  // maxWidth: '28rem',
  width: '100%',
};

const animatedBorderStyle: React.CSSProperties = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  borderRadius: 12,
  background: 'white',
  backgroundSize: '200% 200%',
  // transformOrigin: left center;
  animation: 'pulse 2s linear infinite',
};

const innerContentStyle: React.CSSProperties = {
  position: 'absolute',
  top: '2px',
  left: '2px',
  right: '2px',
  bottom: '2px',
  borderRadius: 12,
  backgroundColor: 'white',
};

const contentStyle: React.CSSProperties = {
  position: 'relative',
  zIndex: 10,
  textAlign: 'center',
};

const titleStyle: React.CSSProperties = {
  fontSize: '1.5rem',
  fontWeight: 'bold',
  color: '#1f2937',
  marginBottom: '1rem',
};

const textStyle: React.CSSProperties = {
  color: '#4b5563',
};

export default PromptInput;
