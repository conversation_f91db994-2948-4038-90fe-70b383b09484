import React from 'react';
import PromptInput from './PromptInput';
import SuggestionList from './SuggestionList';
import {useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import Beta<PERSON><PERSON><PERSON>UI from './BetaSignupUI';

interface ITextBoxContent {
  prompt: string;
  handlePromptChange: (value: string) => void;
  handlePromptSubmit: () => void;
  handleSuggestionClick: (suggestion: string) => void;
}

const TextBoxContent = ({prompt, handlePromptChange, handlePromptSubmit, handleSuggestionClick}: ITextBoxContent) => {
  const {userLoggedIn, user} = useSelector((state: EditorRootState) => state.user);

  // Check if user has beta access
  const userHasBetaAccess = userLoggedIn ? user?.isTileBetaAccessEnabled !== false : true;

  const handleRequestBetaAccess = () => {
    // Redirect to tile.dev website
    window.open('https://www.tile.dev', '_blank');
  };

  return (
    <>
      {userHasBetaAccess ? (
        <>
          {userLoggedIn ? (
            <p style={style.subTitleText}>What do you want to build today?</p>
          ) : (
            <p style={style.subTitleText}>Build Better. Ship Faster</p>
          )}
          <p style={style.titleText}>Launch production ready mobile apps with expert AI Agents</p>
          <div style={style.wrapper}>
            <div style={style.container}>
              <PromptInput value={prompt} onChange={handlePromptChange} onSubmit={handlePromptSubmit} />
              <SuggestionList onSuggestionClick={handleSuggestionClick} />
            </div>
          </div>
        </>
      ) : (
        <BetaSignupUI onRequestAccess={handleRequestBetaAccess} />
      )}
    </>
  );
};

export default TextBoxContent;
const style = {
  titleText: {
    color: '#FAFAFA',
    fontFamily: 'General Sans',
    fontSize: 'clamp(12px, 3vw, 20px)',
    fontStyle: 'normal',
    fontWeight: 300,
    marginTop: 'clamp(8px, 2vw, 22px)',
    textAlign: 'center',
    lineHeight: 'normal',
  },
  subTitleText: {
    color: '#fff',
    fontFamily: 'General Sans',
    fontSize: 'clamp(28px, 8vw, 60px)',
    fontWeight: 500,
    margin: '0px',
    letterSpacing: '-1.2px',
    lineHeight: '62px',
  },
  wrapper: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
  },
  container: {
    width: window.innerWidth <= 700 ? 'clamp(300px, 80vw, 600px)' : '726px',
    marginTop: 40,
  },
};
