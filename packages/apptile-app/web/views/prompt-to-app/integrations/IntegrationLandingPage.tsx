import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity, ScrollView, StyleSheet, Image} from 'react-native';
import availableIntegrations from './availableIntegrations.json';
import {useNavigate, useParams} from '@/root/web/routing.web';
import chatGptLogo from '../../../assets/images/chatGptLogo.png';
import pexels from '../../../assets/images/pexels.png';
import shopify from '../../../assets/images/shopifyLogo.png';
import elevenLab from '../../../assets/images/elevenLabLogo.png';
import supabase from '../../../assets/images/supaBaseLogo.png';
import barLogo from '../../../assets/images/barLogo.png';
import LogoRender from './Components/LogoRender';
import TagWithIcon from './Components/TagWithIcon';
import IntegrationsApi from '../../../..../../api/IntegrationsApi';
import GradientLoader from '@/root/web/components/GradientLoader';
import theme from '../styles-prompt-to-app/theme';
import Analytics from '@/root/web/lib/segment';

type Integration = {
  id: string;
  title: string;
  excerpt: string;
  icon: string;
  integrationCode: string;
  tags: any[];
  priceType: string;
  price: string;
  comingSoon?: boolean;
  logo?: string;
};

interface IntegrationCardProps {
  integration: Integration;
  activeTab: string;
}

export const getPriceBadgeStyle = (priceType: string) => {
  switch (priceType) {
    case 'free':
      return styles.priceBadgeFree;
    case 'paid':
      return styles.priceBadgePaid;
    case 'coming-soon':
      return styles.priceBadgeComingSoon;
    default:
      return undefined;
  }
};
const IntegrationCard: React.FC<IntegrationCardProps> = ({integration, activeTab}) => {
  const navigate = useNavigate();
  const priceBadgeStyle = getPriceBadgeStyle(integration.priceType);
  const [isHovered, setIsHovered] = useState(false);
  return (
    <TouchableOpacity
      disabled={integration?.comingSoon}
      onPress={() => {
        navigate(`../connect/${integration.integrationCode}`);
      }}>
      <View
        style={[
          styles.integrationCard,
          isHovered && styles.integrationCardHovered, // add a new style for hover
        ]}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}>
        <View style={styles.cardContent}>
          <LogoRender integration={integration} style={styles.iconStyles} />
          <View style={styles.integrationInfo}>
            <View style={styles.integrationHeader}>
              <Text style={styles.integrationName}>{integration.title}</Text>
              {integration?.comingSoon ? (
                <Text style={priceBadgeStyle ? [styles.priceBadge, priceBadgeStyle] : styles.priceBadge}>
                  Coming Soon
                </Text>
              ) : activeTab === 'all-integrations' ? (
                <Text style={priceBadgeStyle ? [styles.priceBadge, priceBadgeStyle] : styles.priceBadge}>
                  {integration.price}
                </Text>
              ) : (
                <View style={styles.activeStatusContainer}>
                  <View style={styles.dot} />
                  <Text style={styles.activeStatusText}>Active</Text>
                </View>
              )}
            </View>
            <Text style={styles.integrationDescription}>{integration.excerpt}</Text>
            <View style={styles.tags}>
              {integration?.tags?.map((tag, index) => (
                <TagWithIcon key={index} tag={tag} />
              ))}
            </View>
          </View>
        </View>
        <TouchableOpacity
          style={{paddingRight: 'clamp(8px, 2vw, 40px)'}}
          onPress={() => {
            navigate(`../connect/${integration.integrationCode}`);
          }}>
          <View style={{justifyContent: 'center', flexDirection: 'row', alignItems: 'center', alignContent: 'center'}}>
            {' '}
            {!integration?.comingSoon && (
              <>
                <Text style={styles.detailsButton}>{activeTab === 'all-integrations' ? 'Details' : 'Manage'}</Text>
                <Text>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="22" viewBox="0 0 24 22" fill="none">
                    <path
                      d="M4 12.5H20M20 12.5L14 6.5M20 12.5L14 18.5"
                      stroke="#EFEFEF"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </Text>
              </>
            )}
          </View>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const IntegrationLandingPage = () => {
  const [activeTab, setActiveTab] = useState('all-integrations');
  const [loading, setLoading] = useState(true);
  const [myIntegrations, setMyIntegrations] = useState<any[]>([]);
  const excludeCodes = ['apptile-media-pexels'];

  const params = useParams<{id?: string}>();
  const appId = params?.id || '';
  const IntegrationsLogo = [
    {name: 'Integration 1', source: supabase},
    {name: 'Integration 2', source: pexels},
    {name: 'Integration 3', source: chatGptLogo},
    {name: 'Integration 4', source: shopify},
    {name: 'Integration 5', source: elevenLab},
    {name: 'Integration 6', source: barLogo},
  ];

  const fetchIntegrations = async () => {
    setLoading(true);
    try {
      const integrationResponse: any = await IntegrationsApi.fetchAppIntegrations(appId);
      const integrationData: any = integrationResponse.data;
      if (integrationData) {
        const integrationCodes = integrationData.map(item => item.integrationCode);
        const matched = availableIntegrations.filter(
          item =>
            integrationCodes.includes(item.integrationCode) ||
            excludeCodes.includes(item.integrationCode)
        );

        setMyIntegrations(matched);
      }
    } catch (e) {
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchIntegrations();
    Analytics.pageView('agent_store_viewed');
  }, [appId]);

  if (loading) {
    return (
      <div
        style={{
          width: '100%',
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor:theme.BACKGROUND
        }}>
        <GradientLoader size={75} thickness={5} backgroundColor={theme.BACKGROUND} />
      </div>
    );
  }

  return (
    <View style={styles.mainContainer}>
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          zIndex: 0,
          backgroundColor: '#121212',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <div
          style={{
            height: 300,
            width: 300,
            backgroundColor: '#0DACFF',
            filter: 'blur(100px)',
          }}
        />
      </div>
      <ScrollView
        scrollEnabled={true}
        showsVerticalScrollIndicator={false}
        style={styles.integrationPlatform}
        contentContainerStyle={{paddingBottom: 32, zIndex: 1}}>
        <View style={styles.headerSection}>
          <Text style={styles.mainTitle}>Agent Store</Text>
          <Text style={styles.subtitle}>
            Supercharge your app with AI agents that build
            {'\n'}
            Tiles integrated with your favorite tools
          </Text>
        </View>

        {/* <div style={[styles.partnersSection, {opacity: 0.8}] }> */}
        <View style={styles.partnersSection}>
          <View style={styles.partnersGrid}>
            {IntegrationsLogo.map((partner, index) => (
              <View key={index} style={styles.partnerLogo}>
                <Image source={partner.source} style={styles.iconStyles2} />
              </View>
            ))}
          </View>
        </View>
        <View style={styles.mainSection}>
      { myIntegrations.length>0 &&    <View style={styles.tabsSection}>
            <View style={styles.tabs}>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'my-integrations' && styles.active]}
                onPress={() => setActiveTab('my-integrations')}>
                <Text style={styles.tabText}>My Integrations</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'all-integrations' && styles.active]}
                onPress={() => setActiveTab('all-integrations')}>
                <Text style={styles.tabText}>All Integrations</Text>
              </TouchableOpacity>
            </View>
          </View>}
          {activeTab === 'my-integrations'
            ? myIntegrations.map(integration => (
                <IntegrationCard key={integration.id} integration={integration} activeTab={activeTab} />
              ))
            : availableIntegrations.map(integration => (
                <IntegrationCard key={integration.id} integration={integration} activeTab={activeTab} />
              ))}
        </View>
      </ScrollView>
    </View>
  );
};

export const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    backgroundColor: '#121212',
    minHeight: '100vh',
    width: '100%',
  },
  integrationPlatform: {
    flex: 1,
    display: 'flex',
    maxHeight: '100vh',
    paddingTop: 'clamp(20px, 5vw, 54px)',
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 48,
  },
  mainTitle: {
    fontSize: 'clamp(18px, 5vw, 54px)',
    fontWeight: '600',
    fontFamily: 'General Sans',
    marginBottom: 'clamp(12px, 8vw, 28px)',
    color: '#ffffff',
    letterSpacing: 1.2,
  },
  subtitle: {
    fontSize: 'clamp(10px, 2vw, 20px)',
    textAlign: 'center',
    color: '#E1E1E1',
    textAlignVertical: 'center',
    fontWeight: '400',
    letterSpacing: 0.44,
    fontFamily: 'General Sans',
  },
  partnersSection: {
    marginBottom: 'clamp(18px , 4vh, 38px)',
  },
  partnersGrid: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
    // opacity: 0.7,
  },
  partnerLogo: {
    width: 'clamp(60px, 8vw, 100px)',
    height: 'clamp(60px, 8vw, 100px)',
    backgroundColor: 'rgba(0, 0, 0, 0.15)',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(29, 103, 241, 0.09)',
    marginHorizontal: 24,
    marginVertical: 8,
    shadowColor: 'rgba(255, 255, 255, 0.07)',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 1,
    shadowRadius: 25,
    elevation: 5,
  },
  partnerSymbol: {
    fontSize: 'clamp(10px, 5vw, 96px)',
  },
  pexelsText: {
    fontSize: 14.4,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  elevenLabsText: {
    fontSize: 11.2,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 19.2,
  },
  tabsSection: {
    alignItems: 'flex-start',
    marginBottom: 'clamp(10px, 1vw, 24px)',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#222528',
    borderRadius: 'clamp(5px, 2vw, 9px)',
  },
  tab: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 'clamp(6px, 2vw, 12px)',
    paddingHorizontal: 'clamp(8px, 2vw,24px)',
    borderRadius: 'clamp(5px, 2vw, 8px)',
    fontSize: 'clamp(10px, 2vw, 16px)',
    fontWeight: '500',
  },
  active: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabText: {
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontWeight: '500',
    letterSpacing: 0.32,
  },
  activeTabText: {
    color: '#EFEFEF',
  },
  mainSection: {
    width: '80%',
    alignSelf: 'center',
  },
  integrationCard: {
    backgroundColor: 'rgba(18, 18, 18, 0.3)',
    borderWidth: 0.1,
    borderColor: '#3D3D3D',
    borderRadius: 20,
    padding: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 12,
    transition: 'all 0.2s ease-in-out',
  },
  integrationCardHovered: {
    backgroundColor: 'rgba(18, 18, 18, 0.8)',
    borderWidth: 0.1,
    borderColor: 'rgba(14, 74, 142, 0.6)',
    transform: [{scale: 1.02}],
    cursor: 'pointer', // show pointer on hover
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    flex: 1,
  },
  integrationLogo: {
    width: 'clamp(40px, 8vw, 125px)',
    height: 'clamp(40px, 8vw, 125px)',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(72, 72, 72, 0.2)',
    marginRight: 16,
  },
  logoSymbol: {
    fontSize: 'clamp(10px, 2vw, 24px)',
  },
  integrationInfo: {
    flex: 1,
  },
  integrationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  integrationName: {
    fontSize: 'clamp(10px, 2vw, 20px)',
    fontWeight: '600',
    fontFamily: 'General Sans',
    color: '#EFEFEF',
    letterSpacing: 0.48,
  },

  priceBadge: {
    paddingVertical: 'clamp(2px, 2vw, 4px)',
    paddingHorizontal: 'clamp(6px, 2vw, 12px)',
    borderRadius: 20,
    fontSize: 'clamp(4px, 2vw, 12px)',
    color: '#EFEFEF',
    fontFamily: 'General Sans',
    fontWeight: '500',
    letterSpacing: 0.32,
    marginHorizontal: 'clamp(6px, 2vw, 14px)',
  },
  priceBadgeFree: {
    backgroundColor: '#007AFF',
  },
  priceBadgePaid: {
    backgroundColor: '#007AFF',
  },
  priceBadgeComingSoon: {
    backgroundColor: '#000',
    borderWidth: 1,
    borderColor: '#4E4E4E',
  },
  integrationDescription: {
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    marginBottom: 12,
    fontFamily: 'General Sans',
    fontWeight: '400',
    letterSpacing: 0.36,
  },

  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 'clamp(2px, 2vw, 4px)',
  },
  tag: {
    backgroundColor: '#3D3D3D',
    color: '#e2e8f0',
    paddingVertical: 'clamp(2px, 2vw, 6px)',
    paddingHorizontal: 'clamp(4px, 2vw, 6px)',
    borderRadius: 20,
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontWeight: '500',
    marginRight: 8,
    marginBottom: 4,
  },
  detailsButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontFamily: 'General Sans',
    fontWeight: '400',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 8,
    letterSpacing: 0.4,
  },
  iconStyles: {
    width: 'clamp(60px, 8vw, 100px)',
    height: 'clamp(60px, 8vw, 100px)',
    aspectRatio: 1 / 1,
    borderRadius: 12,
    resizeMode: 'contain',
    borderWidth:1,
    borderColor:'white',
  },
  iconStyles2: {
    width: 'clamp(60px, 8vw, 100px)',
    height: 'clamp(60px, 8vw, 100px)',
    aspectRatio: 1 / 1,
    borderRadius: 12,
    resizeMode: 'contain',
    // borderWidth:1,
    // borderColor:'#3D3D3D',
  },
  activeStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#19D885',
    borderWidth: 1,
    borderRadius: 20,
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    paddingVertical: 'clamp(2px, 2vw, 4px)',
    paddingHorizontal: 'clamp(6px, 2vw, 12px)',
    marginHorizontal: 'clamp(6px, 2vw, 14px)',
  },
  dot: {
    width: 'clamp(4px, 2vw, 8px)',
    height: 'clamp(4px, 2vw, 8px)',
    borderRadius: 9,
    backgroundColor: '#19D885',
    marginRight: 'clamp(4px, 2vw, 8px)',
  },
  activeStatusText: {
    color: '#19D885',
    fontSize: 'clamp(4px, 2vw, 12px)',
    fontWeight: '500',
    letterSpacing: 0.32,
    fontFamily: 'General Sans',
  },
});

export default IntegrationLandingPage;
