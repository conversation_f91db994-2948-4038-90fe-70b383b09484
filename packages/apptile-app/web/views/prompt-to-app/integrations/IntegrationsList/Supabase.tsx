import {useParams} from '@/root/web/routing.web';
import _ from 'lodash';
import React, {act} from 'react';
import theme from '../../styles-prompt-to-app/theme';
import {useState, useEffect, useCallback, useRef, useMemo} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, ScrollView, SafeAreaView, TextInput, StatusBar} from 'react-native';
import {useNavigate} from '@/root/web/routing.web';
import IntegrationBanner from '.././Components/IntegrationBanner';
import RelatedAgents from '.././Components/RelatedAgents';
import IntegrationDetail from '.././Components/IntegrationDetail';
import IntegrationHeader from '.././Components/IntegrationHeader';
import Tabs from '.././Components/Tabs';
import IntegrationPricing from '.././Components/IntegrationPricing';
import axios from 'axios';
import {SupabaseApi, SupabaseProject} from '@/root/web/integrations/supabase/services/SupabaseApi';
import SupabaseConfig from '@/root/web/integrations/supabase/config';
import IntegrationsApi from '@/root/web/api/IntegrationsApi';
import {useDispatch} from 'react-redux';
import {configureDatasources, fetchAppIntegrations} from '@/root/web/actions/editorActions';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import CustomModal from '@/root/web/views/prompt-to-app/integrations/Components/CustomModal';
import Analytics from '@/root/web/lib/segment';

interface SupabaseConnectModalProps {
  onClose: () => void;
  projects: SupabaseProject[];
  isLoading: boolean;
  isRefreshing:boolean,
  isConnecting: boolean;
  isDisconnecting: boolean;
  handleRefreshProjects: () => void;
  handleCreateProject: () => void;
  handleSelectProject: (project: SupabaseProject) => void;
  handleDisconnectProject: (project: SupabaseProject, e: any) => void;
}

const SupabaseConnectModal: React.FC<SupabaseConnectModalProps> = ({
  onClose,
  projects,
  isLoading,
  isRefreshing,
  isConnecting,
  isDisconnecting,
  handleRefreshProjects,
  handleCreateProject,
  handleSelectProject,
  handleDisconnectProject,
}) => {
  const [selectedProject, setSelectedProject] = useState<SupabaseProject | null>(null);

  useEffect(() => {
    if (projects.length > 0) {
      // If there's a selected project, check if its connection status needs an update.
      if (selectedProject) {
        const updatedProject = projects.find(p => p.id === selectedProject.id);
        if (updatedProject && updatedProject.isConnected !== selectedProject.isConnected) {
          setSelectedProject(updatedProject);
        }
      } else {
        // If no project is selected, initialize with the connected one or the first in the list.
        const connected = projects.find(p => p.isConnected);
        setSelectedProject(connected || projects[0]);
      }
    }
  }, [projects, selectedProject]);

  const handleSelectProjectCard = (project: SupabaseProject) => {
    setSelectedProject(project);
  };

  if (!selectedProject) {
    return (
      <View style={stylesx.modal}>
        <Text style={stylesx.title}>Loading projects...</Text>
      </View>
    );
  }

  const isStatusUnavailable =
    !selectedProject.status ||
    selectedProject.status.toLowerCase() === 'inactive' ||
    selectedProject.status.toLowerCase() === 'error';

  const handleConnect = () => {
    if (isStatusUnavailable) {
      window.open(`https://app.supabase.com/project/${selectedProject.id}`, '_blank');
    } else {
      handleSelectProject(selectedProject);
      const timer = setTimeout(() => {
       onClose();
      }, 2000);
      // clearTimeout(timer);
    }
  };
  const handleDisconnect = (e:any) =>{

    handleDisconnectProject(selectedProject, e)
    const timer = setTimeout(() => {
      onClose();
     }, 2000);
    //  clearTimeout(timer);
  }

  return (
    <View style={stylesx.modal}>
      <View style={stylesx.header}>
        <View style={styless.headerTitleGroup}>
          <Text style={stylesx.title}>Supabase Projects</Text>
          <TouchableOpacity
            onPress={() => {
              handleRefreshProjects();
            }}
            style={styless.refreshContainer}>
            <RefreshCwIcon style={{color: '#8A8A8E', marginLeft: 8}} />
            <Text style={styless.refreshText}>{ isRefreshing? 'Refreshing...' : 'Refresh'}</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={onClose} style={stylesx.closeButton}>
          <Text style={stylesx.closeButtonText}>×</Text>
        </TouchableOpacity>
      </View>

      <Text style={stylesx.subtitle}>Select a project connect</Text>

      <ScrollView style={stylesx.projectsContainer}>
        {projects.map(project => (
          <TouchableOpacity
            key={project.id}
            style={[
              stylesx.projectCard,
              project.isConnected && stylesx.connectedCard,
              selectedProject.id === project.id && stylesx.selectedCard,
            ]}
            onPress={() => handleSelectProjectCard(project)}
            activeOpacity={0.7}>
            <View style={stylesx.projectLeft}>
              <Text style={stylesx.projectName}>{project.name}</Text>
              <Text style={stylesx.projectRegion}>
                Region: <Text style={stylesx.regionText}>{project.region}</Text>
              </Text>
            </View>

            <View style={stylesx.projectRight}>
              {project.isConnected && <Text style={stylesx.connectedText}>Connected</Text>}
              <Text style={stylesx.projectStatus}>
                Status: <Text style={stylesx.statusText}>{project.status}</Text>
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <TouchableOpacity style={stylesx.createNewButton} onPress={handleCreateProject} activeOpacity={0.7}>
        <Text style={stylesx.createNewButtonText}>+ Create New</Text>
      </TouchableOpacity>

      {!selectedProject?.isConnected ? (
        <TouchableOpacity style={stylesx.connectButton} onPress={handleConnect} activeOpacity={0.7}>
          <Text style={stylesx.connectButtonText}>{isConnecting ? 'Connecting...' : 'Connect'}</Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={stylesx.disonnectButton}
          onPress={e => handleDisconnect(e)}
          activeOpacity={0.7}>
          <Text style={stylesx.connectButtonText}>{isDisconnecting ? 'Disconnecting...' : 'Disconnect'}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

interface ProjectCardProps {
  project: SupabaseProject;
  isConnected: boolean;
  onPress: (project: SupabaseProject) => void;
  selectedProject: SupabaseProject | null;
}

const ProjectCard: React.FC<ProjectCardProps> = ({project, isConnected, onPress, selectedProject}) => {
  const cardStyle = [styless.projectCard, isConnected ? styless.projectCardConnected : {}];

  return (
    <TouchableOpacity
      style={[cardStyle, selectedProject?.id === project.id && stylesx.selectedCard]}
      onPress={() => onPress(project)}
      activeOpacity={0.7}>
      <View style={styless.projectCardHeader}>
        <Text style={styless.projectCardTitle}>{project.name}</Text>
        {isConnected && <Text style={styless.projectCardConnectedText}>Connected</Text>}
      </View>
      <View style={styless.projectCardDetails}>
        <Text style={styless.projectCardInfoText}>Region: {project.region}</Text>
        <Text style={styless.projectCardInfoText}>Status: {project.status}</Text>
      </View>
    </TouchableOpacity>
  );
};

interface IntegrationConfigProps {
  projects: SupabaseProject[];
  isLoading: boolean;
  isRefreshing: boolean,
  isConnecting: boolean;
  isDisconnecting: boolean;
  status: string | null;
  handleRefreshProjects: () => void;
  handleCreateProject: () => void;
  handleSelectProject: (project: SupabaseProject) => void;
  handleDisconnectProject: (project: SupabaseProject, e: any) => void;
}

const IntegrationConfig: React.FC<IntegrationConfigProps> = ({
  projects,
  isLoading,
  isRefreshing,
  isConnecting,
  isDisconnecting,
  handleRefreshProjects,
  handleCreateProject,
  handleSelectProject,
  handleDisconnectProject,
}) => {
  const [selectedProject, setSelectedProject] = useState<SupabaseProject | null>(null);

  useEffect(() => {
    if (projects.length > 0) {
      // If there's a selected project, check if its connection status needs an update.
      if (selectedProject) {
        const updatedProject = projects.find(p => p.id === selectedProject.id);
        if (updatedProject && updatedProject.isConnected !== selectedProject.isConnected) {
          setSelectedProject(updatedProject);
        }
      } else {
        // If no project is selected, initialize with the connected one or the first in the list.
        const connected = projects.find(p => p.isConnected);
        setSelectedProject(connected || projects[0]);
      }
    }
  }, [projects, selectedProject]);

  if (!selectedProject) {
    return (
      <SafeAreaView style={styless.appContainer}>
        <View style={styless.contentContainer}>
          <Text style={styless.headerTitle}>Loading configuration...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const isStatusUnavailable =
    !selectedProject.status ||
    selectedProject.status.toLowerCase() === 'inactive' ||
    selectedProject.status.toLowerCase() === 'error';

  const handleConnect = () => {
    if (isStatusUnavailable) {
      window.open(`https://app.supabase.com/project/${selectedProject.id}`, '_blank');
    } else {
      handleSelectProject(selectedProject);
    }
  };

  const handleProjectSelect = (project: SupabaseProject) => {
    setSelectedProject(project);
  };

  return (
    <SafeAreaView style={styless.appContainer}>
      <StatusBar barStyle="light-content" />
      <View style={styless.contentContainer}>
        {/* Header Section */}
        <View style={styless.header}>
          <View style={styless.headerTitleGroup}>
            <Text style={styless.headerTitle}>Supabase Projects</Text>
            <TouchableOpacity onPress={handleRefreshProjects} style={styless.refreshContainer}>
              <RefreshCwIcon style={{color: '#8A8A8E', marginLeft: 8}} />
              <Text style={styless.refreshText}>{isRefreshing ? 'Refreshing...' : 'Refresh'}</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={styless.createNewButton}
            onPress={handleCreateProject}
            disabled={isLoading}
            activeOpacity={0.7}>
            <PlusIcon style={{color: '#FFFFFF', marginRight:'clamp(2px, 1vw, 8px'}} />
            <Text style={styless.createNewButtonText}>Create New</Text>
          </TouchableOpacity>
        </View>
        <Text style={styless.subHeader}>Select a project to connect</Text>
        <View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styless.projectList}>
            {projects.length > 0 &&
              projects.map(item => (
                <ProjectCard
                  key={item.id}
                  project={item}
                  isConnected={!!item.isConnected}
                  onPress={handleProjectSelect}
                  selectedProject={selectedProject}
                />
              ))}
          </ScrollView>
        </View>
        <View style={styless.disconnectContainer}>
          {!selectedProject?.isConnected ? (
            <TouchableOpacity style={styless.connectButton} onPress={handleConnect} activeOpacity={0.7}>
              <Text style={styless.disconnectButtonText}>{isConnecting ? 'Connecting...' : 'Connect'}</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styless.disconnectButton}
              onPress={e => handleDisconnectProject(selectedProject, e)}
              activeOpacity={0.7}>
              <Text style={styless.disconnectButtonText}>{isDisconnecting ? 'Disconnecting...' : 'Disconnect'}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const Supabase = ({integration}: any) => {
  const navigate = useNavigate();
  const {id: urlAppId, orgId} = useParams<{id: string; orgId: string}>();
  const effectiveAppId = urlAppId;
  const dispatch = useDispatch();

  const [activeTab, setActiveTab] = useState('details');
  const [showConfig, setShowConfig] = useState(false);
  const [showDetails, setShowDetails] = useState(true);
  const [showPricing, setShowPricing] = useState(false);
  const [showIntegrationBanner, setShowIntegrationBanner] = useState(true);

  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [showProjectsModal, setShowProjectsModal] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [projects, setProjects] = useState<SupabaseProject[]>([]);
  const [_selectedProject, setSelectedProject] = useState<SupabaseProject | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectedProject, setConnectedProject] = useState<SupabaseProject | null>(null);
  const [ModalVisible, setModalVisible] = useState(false);

  useMountEffect(() => {
    dispatch(fetchAppIntegrations(effectiveAppId as string));
  });

  useEffect(() => {
    Analytics.track('agent_integration_viewed', {
      integration: integration.title,
    });
  }, []);

  const [credentials, setCredentials] = useState({
    supabaseAccessToken: '',
    supabaseRefreshToken: '',
    accessTokenExpiresIn: 86400,
    apptileSupabaseClientId: '',
    apptileSupabaseClientSecret: '',
  });

  const exchangeCodeForToken = useCallback(
    async (code: string, state: string) => {
      setIsLoading(true);
      setError(null);
      setStatus('Exchanging authorization code for access token...');

      try {
        // Verify the state parameter to prevent CSRF attacks
        const savedState = localStorage.getItem('supabase_auth_state');
        if (state !== savedState) {
          throw new Error('Invalid state parameter');
        }

        // Use the proxy route to avoid CORS issues
        const formData = new URLSearchParams();
        formData.append('grant_type', 'authorization_code');
        formData.append('code', code);
        formData.append('client_id', SupabaseConfig.CLIENT_ID);
        formData.append('client_secret', SupabaseConfig.CLIENT_SECRET);
        formData.append('redirect_uri', `${window.location.origin}${SupabaseConfig.REDIRECT_URI}?orgId=${orgId}`);

        // Use the proxy route instead of directly calling the Supabase API
        const proxyUrl = `${window.PLUGIN_SERVER_URL}/cli/supabase-proxy/oauth/token`;

        const tokenResponse = await fetch(proxyUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData.toString(),
        });

        if (!tokenResponse.ok) {
          throw new Error(`Failed to exchange code for token: ${tokenResponse.statusText}`);
        }

        const tokenData = await tokenResponse.json();

        // Set the credentials
        const newCredentials = {
          supabaseAccessToken: tokenData.access_token,
          supabaseRefreshToken: tokenData.refresh_token,
          accessTokenExpiresIn: tokenData.expires_in || SupabaseConfig.ACCESS_TOKEN_EXPIRES_IN,
          apptileSupabaseClientId: SupabaseConfig.CLIENT_ID,
          apptileSupabaseClientSecret: SupabaseConfig.CLIENT_SECRET,
        };
        setCredentials(newCredentials);

        try {
          //Also update supabase credentials in the DB
          await SupabaseApi.updateProjectCredentials(effectiveAppId, {
            SUPABASE_ACCESS_TOKEN: tokenData.access_token,
            SUPABASE_REFRESH_TOKEN: tokenData.refresh_token,
            ACCESS_TOKEN_EXPIRES_IN: tokenData.expires_in || SupabaseConfig.ACCESS_TOKEN_EXPIRES_IN,
          });
        } catch (err) {
          console.info('Update failed the db might be empty', err);
        }
        const projectsList = await SupabaseApi.listProjects(newCredentials.supabaseAccessToken);

        // Check if any project is already connected
        if (connectedProject) {
          // Mark the connected project
          projectsList.forEach(project => {
            if (project.id === connectedProject.id) {
              project.isConnected = true;
            }
          });
        }

        setProjects(projectsList);
        setShowProjectsModal(true);
        setModalVisible(true);
        setStatus('Select a Supabase project to connect:');
        setIsLoading(false);
      } catch (err) {
        console.error('Error exchanging code for token:', err);
        setError('Failed to authenticate with Supabase');
        setStatus(null);
        setIsLoading(false);
      }
    },
    [connectedProject, effectiveAppId, orgId],
  );
  const handleConnect = useCallback(() => {
    setIsLoading(true);
    setError(null);
    setStatus('Connecting to Supabase...');

    try {
      // Generate a random state value for security
      const state = Math.random().toString(36).substring(2, 15);

      // Store the state in localStorage to verify later
      localStorage.setItem('supabase_auth_state', state);

      // Construct the OAuth URL
      const redirectUri = `${window.location.origin}${SupabaseConfig.REDIRECT_URI}?orgId=${orgId}`;

      const oauthUrl = `${SupabaseConfig.OAUTH_AUTHORIZE_URL}?client_id=${
        SupabaseConfig.CLIENT_ID
      }&redirect_uri=${encodeURIComponent(redirectUri)}&state=${state}&response_type=code`;

      // Open the OAuth window
      window.open(oauthUrl, 'Supabase Auth', 'width=600,height=600');
    } catch (err) {
      console.error('Error initiating Supabase OAuth:', err);
      setError('Failed to connect to Supabase');
      setStatus(null);
      setIsLoading(false);
    }
  }, [orgId]);

  const handleSelectProject = async (project: SupabaseProject) => {
    // If the project is already connected, don't do anything
    if (project.isConnected) {
      return;
    }

    setSelectedProject(project);
    setIsConnecting(true);
    setError(null);

    try {
      // Check if there's already a connected project
      const connectedProj = await SupabaseApi.getConnectedProject(effectiveAppId);

      if (connectedProj) {
        // We're changing the project - update the credentials
        setStatus(`Updating Supabase integration to ${project.name}...`);

        // Get the project's API keys
        const apiKeysResponse = await axios.get(`${SupabaseApi.proxyBaseURL}/projects/${project.id}/api-keys`, {
          headers: {
            Authorization: `Bearer ${credentials.supabaseAccessToken}`,
            'Content-Type': 'application/json',
          },
        });

        // Find the anon key
        const anonKey = apiKeysResponse.data.find((key: any) => key.name === 'anon')?.api_key;

        if (!anonKey) {
          throw new Error('Could not find anon key for the project');
        }

        // Update the credentials
        await SupabaseApi.updateProjectCredentials(effectiveAppId, {
          SUPABASE_ANON_KEY: anonKey,
          SUPABASE_PROJECT_REF: project.id,
          SUPABASE_ACCESS_TOKEN: credentials.supabaseAccessToken,
          SUPABASE_REFRESH_TOKEN: credentials.supabaseRefreshToken,
          ACCESS_TOKEN_EXPIRES_IN: credentials.accessTokenExpiresIn,
          APPTILE_SUPABASE_CLIENT_ID: credentials.apptileSupabaseClientId,
          APPTILE_SUPABASE_CLIENT_SECRET: credentials.apptileSupabaseClientSecret,
        });

        setStatus(`Successfully updated Supabase integration to ${project.name}!`);
      } else {
        // Connect to a new project
        setStatus(`Installing Apptile integration for ${project.name}...`);
        await SupabaseApi.connectToProject(effectiveAppId, project, credentials);
        setStatus(`Successfully installed Apptile integration for ${project.name}!`);
      }
      await IntegrationsApi.toggleAppIntegration(effectiveAppId, 'supabase', true, true);
      //Configure datasources to fetch the new credentials
      if (effectiveAppId) {
        dispatch(configureDatasources(effectiveAppId, true, true));
      }
      // Update the connected project
      setConnectedProject(project);
      setIsConnected(true);
      setShowConfig(true);
      setActiveTab('setupConfig');
      setShowIntegrationBanner(false);

      // Mark this project as connected and update the projects list
      const updatedProjects = projects.map(p => ({
        ...p,
        isConnected: p.id === project.id,
      }));
      setProjects(updatedProjects);

      // Keep the modal open to show the connected status
      setIsConnecting(false);
    } catch (err) {
      console.error('Error connecting to Supabase project:', err);
      setError(`Failed to install Apptile integration for ${project.name}`);
      setStatus(null);
      setIsConnecting(false);
    }
  };

  const handleDisconnectProject = async (project: SupabaseProject, event: any) => {
    // Stop the event from propagating to the parent (which would select the project)
    if (event && event.stopPropagation) {
      event.stopPropagation();
    }

    setIsDisconnecting(true);
    setError(null);
    setStatus(`Disconnecting from ${project.name}...`);

    try {
      const supabaseIntegration = await SupabaseApi.getSupabaseIntegration(effectiveAppId);

      if (!supabaseIntegration) {
        throw new Error('Supabase integration not found');
      }

      // Directly call the toggleAppIntegration API
      await IntegrationsApi.toggleAppIntegration(effectiveAppId, supabaseIntegration.id, false);

      // Update the UI
      setConnectedProject(null);
      setIsConnected(false);
      setShowConfig(false);
      setActiveTab('details');
      setShowIntegrationBanner(true);

      // Update the projects list
      const updatedProjects = projects.map(p => ({
        ...p,
        isConnected: false,
      }));
      setProjects(updatedProjects);

      setStatus(`Successfully disconnected from ${project.name}`);
      setIsDisconnecting(false);
    } catch (err) {
      console.error('Error disconnecting project:', err);
      setError('Failed to disconnect project');
      setStatus(null);
      setIsDisconnecting(false);
    }
  };

  const handleRefreshProjects = async () => {
    setIsRefreshing(true);
    setError(null);
    setStatus('Refreshing projects...');

    try {
      const projectsList = await SupabaseApi.listProjects(credentials.supabaseAccessToken);
      if (connectedProject) {
        // Mark the connected project
        projectsList.forEach(project => {
          if (project.id === connectedProject.id) {
            project.isConnected = true;
          }
        });
      }

      setProjects(projectsList);
      setStatus('Select a Supabase project to connect:');
      setIsRefreshing(false);
    } catch (err) {
      console.error('Error refreshing projects:', err);
      setError('Failed to refresh projects');
      setStatus(null);
     setIsRefreshing(false);
    }
  };

  const handleCreateProject = () => {
    window.open('https://app.supabase.com/new/new-project', '_blank');
  };

  const onActiveTabChange = (value: string) => {
    setActiveTab(value);
  };

  const onConnectIntegration = () => {
    if (isConnected) {
      setShowProjectsModal(true);
      setModalVisible(true), handleRefreshProjects();
      handleRefreshProjects();
    } else {
      handleConnect();
    }
  };

  useEffect(() => {
    // Reset connection state when app ID changes
    setIsConnected(false);
    setConnectedProject(null);
    setError(null);
    setStatus(null);

    const checkConnectedProject = async () => {
      try {
        if (!effectiveAppId) return 'App id not found';
        // Set loading state while checking connection
        setIsLoading(true);
        setStatus('Checking Supabase connection...');

        // Check if this app has a connected Supabase project
        const connectedProject = await SupabaseApi.getConnectedProject(effectiveAppId);
        if (connectedProject) {
          const supabaseCreds = (await IntegrationsApi.fetchAppIntegrationSecrets(effectiveAppId, 'supabase'))
            .data as any;
          if (!supabaseCreds) {
            setIsLoading(false);
            setStatus(null);
            return null;
          }
          setShowConfig(true);
          setActiveTab('setupConfig');
          // setActiveTab('setupConfig');

          // Set the credentials for later use
          setCredentials({
            supabaseAccessToken: supabaseCreds.SUPABASE_ACCESS_TOKEN,
            supabaseRefreshToken: supabaseCreds.SUPABASE_REFRESH_TOKEN,
            accessTokenExpiresIn: supabaseCreds.ACCESS_TOKEN_EXPIRES_IN,
            apptileSupabaseClientId: supabaseCreds.APPTILE_SUPABASE_CLIENT_ID,
            apptileSupabaseClientSecret: supabaseCreds.APPTILE_SUPABASE_CLIENT_SECRET,
          });

          setConnectedProject(connectedProject);

          setIsConnected(true);
          setShowIntegrationBanner(false);
          console.log(`App ${effectiveAppId} has connected Supabase project:`, connectedProject.name);
        } else {
          // Explicitly set to not connected when no project is found
          setIsConnected(false);
          setConnectedProject(null);
          console.log(`App ${effectiveAppId} has no connected Supabase project`);
        }

        // Clear loading state after check is complete
        setIsLoading(false);
        setStatus(null);
      } catch (err) {
        console.error('Error checking connected project:', err);
        // Reset connection state on error
        setIsConnected(false);
        setConnectedProject(null);
        setIsLoading(false);
        setStatus(null);
      }
    };

    checkConnectedProject();
  }, [effectiveAppId]);

  useEffect(() => {
    handleRefreshProjects();
  }, [connectedProject]);

  useEffect(() => {
    const handleOAuthCallback = (event: MessageEvent) => {
      // Verify the origin
      if (event.origin !== window.location.origin) return;

      // Check if this is a Supabase auth callback
      if (event.data && event.data.type === 'supabase_auth_callback') {
        const {code, state} = event.data;

        // Exchange the code for an access token
        exchangeCodeForToken(code, state);
      }
    };

    // Add the event listener
    window.addEventListener('message', handleOAuthCallback);

    // Clean up
    return () => {
      window.removeEventListener('message', handleOAuthCallback);
    };
  }, [exchangeCodeForToken]);

  const memoizedModal = useMemo(() => {
    if (!showProjectsModal) return null;

    return (
      <CustomModal
        visible={ModalVisible}
        onClose={() => {
          setModalVisible(false);
        }}
        showCloseButton={true}
        backdropOpacity={0.7}
        closeOnBackdropPress={false}
        modalStyle={styles.customModalStyle}
        contentStyle={styles.customContentStyle}>
        <SupabaseConnectModal
          onClose={() => setModalVisible(false)}
          projects={projects}
          isLoading={isLoading}
          isRefreshing={isRefreshing}
          isConnecting={isConnecting}
          isDisconnecting={isDisconnecting}
          handleRefreshProjects={handleRefreshProjects}
          handleCreateProject={handleCreateProject}
          handleSelectProject={handleSelectProject}
          handleDisconnectProject={handleDisconnectProject}
        />
      </CustomModal>
    );
  }, [
    showProjectsModal,
    ModalVisible,
    projects,
    isLoading,
    isConnecting,
    isDisconnecting,
    handleRefreshProjects,
    handleCreateProject,
    handleSelectProject,
    handleDisconnectProject,
  ]);

  return (
    <View style={{width: '100%', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
      <TouchableOpacity
        style={styles.backcontainer}
        onPress={() => {
          navigate('../');
        }}
        activeOpacity={0.7}>
        <View style={styles.content}>
          <Text style={styles.arrow}>←</Text>
          <Text style={styles.text}>Back</Text>
        </View>
      </TouchableOpacity>
      <View style={styles.integrationsSection}>
        <IntegrationHeader
          integration={integration}
          isConnected={isConnected}
          isLoading={isLoading}
          onClick={onConnectIntegration}
        />
      </View>
      <View style={{width: '80%', flex: 1}}>
       { isConnected && <Tabs
          IactiveTab={activeTab}
          onChange={onActiveTabChange}
          showConfig={showConfig}
          showPricing={showPricing}
          showDetails={showDetails}
        />}
      <ScrollView
          style={{ flex: 1}}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{justifyContent: 'center', alignItems: 'center'}}>
          {activeTab === 'details' ? (
            <IntegrationDetail integration={integration} />
          ) : activeTab === 'pricing' ? (
            <IntegrationPricing integration={integration} />
          ) : (
            <IntegrationConfig
              projects={projects}
              isLoading={isLoading}
              isRefreshing={isRefreshing}
              isConnecting={isConnecting}
              isDisconnecting={isDisconnecting}
              status={status}
              handleRefreshProjects={handleRefreshProjects}
              handleCreateProject={handleCreateProject}
              handleSelectProject={handleSelectProject}
              handleDisconnectProject={handleDisconnectProject}
            />
          )}
          {showIntegrationBanner && <IntegrationBanner onBannerClick={onConnectIntegration} integration={integration} />}
          <RelatedAgents integration={integration} />
        </ScrollView>
      </View>
      {memoizedModal}
    </View>
  );
};

const RefreshCwIcon = ({style}: {style: React.CSSProperties}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16" // Adjusted size
    height="16" // Adjusted size
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    style={style}>
    <path d="M3 2v6h6" />
    <path d="M21 12A9 9 0 0 0 6 5.3L3 8" />
    <path d="M21 22v-6h-6" />
    <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7" />
  </svg>
);

const PlusIcon = ({style}: {style: React.CSSProperties}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16" // Adjusted size
    height="16" // Adjusted size
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    style={style}>
    <line x1="12" y1="5" x2="12" y2="19" />
    <line x1="5" y1="12" x2="19" y2="12" />
  </svg>
);

const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    minHeight: '100vh',
  },
  integrationsSection: {
    width: '80%',
    alignSelf: 'center',
    justifyContent: 'center',
    alignContent: 'center',
    marginBottom: 'clamp(20px , 4vw, 40px)',
  },
  integrationCard: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginVertical: 12,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    flex: 1,
  },
  integrationLogo: {
    width: 'clamp(40px, 8vw, 125px)',
    height: 'clamp(40px, 8vw, 125px)',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    marginRight: 16,
  },
  logoSymbol: {
    fontSize: 'clamp(10px, 2vw, 24px)',
  },
  integrationInfo: {
    flex: 1,
  },
  integrationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  integrationName: {
    fontSize: 'clamp(10px, 2vw, 24px)',
    fontWeight: '600',
    fontFamily: 'General Sans',
    color: '#EFEFEF',
    letterSpacing: 0.48,
  },

  priceBadge: {
    paddingVertical: 'clamp(2px, 2vw, 4px)',
    paddingHorizontal: 'clamp(6px, 2vw, 12px)',
    borderRadius: 20,
    fontSize: 'clamp(4px, 2vw, 12px)',
    color: '#EFEFEF',
    fontFamily: 'General Sans',
    fontWeight: '500',
    letterSpacing: 0.32,
    marginHorizontal: 'clamp(6px, 2vw, 14px)',
  },
  priceBadgeFree: {
    backgroundColor: '#007AFF',
  },
  priceBadgePaid: {
    backgroundColor: '#007AFF',
  },
  priceBadgeComingSoon: {
    backgroundColor: '#000',
    borderWidth: 1,
    borderColor: '#4E4E4E',
  },
  integrationDescription: {
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    marginBottom: 12,
    colorL: '#EFEFEF',
    fontFamily: 'General Sans',
    fontWeight: '400',
    letterSpacing: 0.36,
  },

  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 'clamp(2px, 2vw, 4px)',
  },
  tag: {
    backgroundColor: '#3D3D3D',
    color: '#e2e8f0',
    paddingVertical: 'clamp(2px, 2vw, 6px)',
    paddingHorizontal: 'clamp(4px, 2vw, 6px)',
    borderRadius: 20,
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontWeight: '500',
    marginRight: 8,
    marginBottom: 4,
  },
  detailsButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontFamily: 'General Sans',
    fontWeight: '400',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    letterSpacing: 0.4,
  },
  activeStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#19D885',
    borderWidth: 1,
    borderRadius: 20,
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    paddingVertical: 'clamp(2px, 2vw, 4px)',
    paddingHorizontal: 'clamp(6px, 2vw, 12px)',
    marginHorizontal: 'clamp(6px, 2vw, 14px)',
  },
  dot: {
    width: 'clamp(4px, 2vw, 8px)',
    height: 'clamp(4px, 2vw, 8px)',
    borderRadius: 9,
    backgroundColor: '#19D885',
    marginRight: 'clamp(4px, 2vw, 8px)',
  },
  activeStatusText: {
    color: '#19D885',
    fontSize: 'clamp(4px, 2vw, 12px)',
    fontWeight: '500',
    letterSpacing: 0.32,
    fontFamily: 'General Sans',
  },
  backcontainer: {
    width: '5%',
    backgroundColor: 'transparent',
    paddingLeft: '10%',
    alignSelf: 'flex-start',
    marginVertical: 20,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrow: {
    fontSize: 18,
    color: '#EFEFEF',
    marginRight: 8,
    fontWeight: '400',
  },
  text: {
    fontSize: 16,
    color: '#EFEFEF',
    fontWeight: '400',
  },
  container: {
    flex: 1,
    // paddingHorizontal: 35,
    paddingVertical: 20,
    // alignItems: 'center',
    backgroundColor: '#121212',
    minHeight: '100vh',
    width: '100%',
    padding: 'clamp(20px, 5vw, 54px)',
  },
  containerwrap: {
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#121212',
  },
  container2: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  iconStyles: {
    width: 'clamp(60px, 8vw, 120px)',
    height: 'clamp(60px, 8vw, 120px)',
    aspectRatio: 1 / 1,
    borderRadius: 12,
    resizeMode: 'contain',
    borderWidth: 1,
    borderColor: '#3D3D3D',
  },
  leftSection: {
    marginRight: 16,
  },
  logo: {
    width: 60,
    height: 60,
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderWidth: 1,
    borderColor: '#3a3a3a',
  },
  logoIcon: {
    fontSize: 24,
    color: '#4ade80', // Green color for the arrow
  },
  middleSection: {
    flex: 1,
    marginRight: 16,
  },
  titleRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600' as const,
    color: 'white',
    marginRight: 12,
  },
  freeBadge: {
    backgroundColor: '#4285f4',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  freeBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500' as const,
  },
  description: {
    color: '#ccc',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  databaseTag: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: '#3a3a3a',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#4a4a4a',
    alignSelf: 'flex-start' as const,
  },
  databaseIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  databaseText: {
    color: '#ccc',
    fontSize: 12,
    fontWeight: '500' as const,
  },
  rightSection: {},
  connectButton: {
    backgroundColor: '#1060E0',
    paddingHorizontal: 48,
    paddingVertical: 12,
    borderRadius: 8,
  },
  connectButtonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: 'General Sans',
    color: '#FFFFFF',
  },
  tabsSection: {
    // flex: 1,
    display: 'flex',
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: 'clamp(10px, 2vw, 28px)',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#222528',
    borderRadius: 'clamp(5px, 2vw, 9px)',
  },
  tab: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 'clamp(6px, 2vw, 12px)',
    paddingHorizontal: 'clamp(8px, 2vw,24px)',
    borderRadius: 'clamp(4px, 2vw, 8px)',
    fontSize: 'clamp(10px, 2vw, 16px)',
    fontWeight: '500',
  },
  active: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabText: {
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontWeight: '500',
    letterSpacing: 0.32,
  },
  activeTabText: {
    color: '#EFEFEF',
  },
  buttonLoading: {
    backgroundColor: '#2EB77E', // Darker shade of Supabase green
    cursor: 'not-allowed',
    opacity: 0.8,
  },
  connectedButton: {
    backgroundColor: '#06402B', // Dark green for connected state
    border: '2px solid #059669',
    boxShadow: '0 4px 8px rgba(16, 185, 129, 0.3)',
    transform: 'translateY(-1px)',
  },
  statusText: {
    color: '#4A5568',
    marginTop: 8,
    fontSize: 12,
  },
  errorText: {
    color: '#E53E3E',
    marginTop: 8,
    fontSize: 12,
  },
  modalOverlay: {
    // position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    // backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 24,
    width: '90%',
    maxWidth: 500,
    maxHeight: '80vh',
    overflow: 'auto',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  },
  modalHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  customModalStyle: {
    // backgroundColor: '#1A1D20',
    borderColor: '#3D3D3D',
    // borderWidth: 1,
    borderRadius:16
  },
  customContentStyle: {
   borderRadius:16
  },
  closeButton: {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    padding: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#718096',
  },
  projectsList: {
    marginTop: 16,
  },
  projectsActions: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  createNewButton: {
    backgroundColor: '#3ECF8E',
    color: 'white',
    borderRadius: 2,
    fontSize: 14,
    padding: 20,
    marginTop: 5,
    fontWeight: 'bold',
    display: 'flex',
    border: 'none',
    cursor: 'pointer',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  iconButton: {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    padding: 4,
    borderRadius: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#718096',
  },
  noProjectsContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#F7FAFC',
    borderRadius: 4,
    border: '1px solid #E2E8F0',
    marginTop: 16,
  },
  noProjectsText: {
    fontSize: 14,
    color: '#4A5568',
    marginBottom: 16,
  },
  createProjectButton: {
    backgroundColor: '#3ECF8E',
    color: 'white',
    border: 'none',
    borderRadius: 4,
    padding: '8px 16px',
    fontSize: 14,
    fontWeight: 'bold',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
  },
  projectItem: {
    backgroundColor: '#F7FAFC',
    borderRadius: 4,
    padding: 12,
    marginBottom: 8,
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
    border: '1px solid #E2E8F0',
    position: 'relative',
  },
  connectedProjectItem: {
    backgroundColor: '#F0FFF4', // Light green background
    border: '1px solid #3ECF8E', // Green border
  },
  unavailableProjectItem: {
    cursor: 'pointer',
    padding: '8px 12px',
  },
  fixStatusBadge: {
    backgroundColor: '#FC8181',
    color: 'white',
    fontSize: 10,
    padding: 5,
    borderRadius: 3,
    marginLeft: 8,
  },
  projectName: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  connectedBadge: {
    backgroundColor: '#3ECF8E',
    color: 'white',
    fontSize: 10,
    padding: '2px 6px',
    borderRadius: 10,
    marginLeft: 8,
  },
  disconnectButton: {
    position: 'absolute',
    top: 6,
    right: 8,
    backgroundColor: '#E53E3E',
    color: 'white',
    border: 'none',
    borderRadius: 2,
    padding: 5,
    fontSize: 12,
    cursor: 'pointer',
  },
  projectDetails: {
    display: 'flex',
    justifyContent: 'space-between',
    fontSize: 12,
    color: '#718096',
  },
  spinner: {
    width: 16,
    height: 16,
    border: '2px solid rgba(255, 255, 255, 0.3)',
    borderTop: '2px solid white',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    marginRight: 8,
  },
});

const styless = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: '#1A1D20',
    width: '100%',
    borderRadius: 20,
  },
  contentContainer: {
    flex: 1,
    padding: 20,
    display: 'flex',
    flexDirection: 'column',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitleGroup: {
    // This view groups the title and refresh text vertically
    flexDirection: 'row',
  },
  headerTitle: {
    fontSize: 'clamp(12px , 2vw, 16px)',
    fontWeight: '600',
    fontFamily: 'General Sans',
    color: '#FFFFFF',
  },
  refreshContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  refreshText: {
    fontSize: 14,
    color: '#8A8A8E',
    marginLeft: 6,
  },
  createNewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#4A4A4A',
    borderRadius: 8,
    paddingVertical: 'clamp(2px, 2vw , 10px)',
    paddingHorizontal: 'clamp(6px, 2vw , 16px)',
  },
  createNewButtonText: {
    color: '#FFFFFF',
    fontSize: 'clamp(8px, 2vw , 14px)',
    fontWeight: '500',
  },
  subHeader: {
    color: '#8A8A8E',
    fontSize: 'clamp(10px, 2vw , 16px)',
    marginBottom: 16,
  },
  projectList: {
    flexDirection:'row',
    justifyContent:'center',
  alignItems:'center',
    flexWrap:'wrap'
  },
  projectCard: {
    padding: 'clamp(10px, 2vw , 24px)',
    borderRadius: 12,
    backgroundColor: '#34363E4D',
    borderWidth: 1,
    borderColor: '#3D3D3D',
    minWidth: 240,
    width: 'clamp(200px, 36vw, 700px)', // Explicit width for horizontal items
    marginHorizontal: 10, // Space between horizontal items
  },
  projectCardConnected: {
    backgroundColor: '#34363E4D',
    // borderColor: '#34C759',
    // borderWidth:0
  },
  projectCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  projectCardTitle: {
    color: '#FFFFFF',
    fontSize: 'clamp(12px, 2vw , 18px)',
    fontWeight: '600',
  },
  projectCardConnectedText: {
    color: '#34C759',
    fontSize: 'clamp(10px, 2vw , 14px)',
    fontWeight: '600',
  },
  projectCardDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  projectCardInfoText: {
    color: '#8A8A8E',
    fontSize: 'clamp(10px, 2vw , 14px)',
  },
  disconnectContainer: {
    marginTop: 'auto', // Pushes button to the bottom
    paddingTop: 12,

    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  connectButton: {
    backgroundColor: '#2563EB',
    alignItems: 'center',
    justifyContent: 'flex-end',
    padding: 12,
    width: 'clamp(60px, 7vw, 180px)',
    borderRadius: 8,
  },
  disconnectButton: {
    backgroundColor: '#EB3223',
    alignItems: 'center',
    justifyContent: 'flex-end',
    padding: 12,
    width: 'clamp(60px, 8vw, 200px)',
    borderRadius: 8,
  },
  disconnectButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    fontSize: 'clamp(8px, 2vw, 14px)'
  },
});

const stylesx = StyleSheet.create({
  modal: {
    backgroundColor: '#1C1C1E',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    alignSelf: 'center',
    // maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 20,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 20,
  },
  subtitle: {
    fontSize: 12,
    color: '#9A9A9A',
    marginBottom: 16,
  },
  projectsContainer: {
    marginBottom: 16,
  },
  projectCard: {
    backgroundColor: '#161618',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#3D3D3D',
  },
  connectedCard: {
    backgroundColor: '#161618',
  },
  selectedCard: {
    borderWidth: 0,
    backgroundColor: '#34363E',
  },
  projectLeft: {
    flex: 1,
  },
  projectName: {
    color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 14,
    marginBottom: 4,
  },
  projectRegion: {
    fontSize: 12,
    color: '#9A9A9A',
  },
  regionText: {
    color: '#B0B0B0',
  },
  projectRight: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  connectedText: {
    color: '#3AB44A',
    fontWeight: '700',
    fontSize: 12,
    marginBottom: 6,
  },
  projectStatus: {
    fontSize: 12,
    color: '#9A9A9A',
  },
  statusText: {
    fontWeight: '700',
    color: '#B0B0B0',
  },
  createNewButton: {
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#6E6E7E',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  createNewButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  connectButton: {
    height: 40,
    backgroundColor: '#2563EB',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    paddingHorizontal: 24,
  },
  disonnectButton: {
    height: 40,
    backgroundColor: '#EB3223',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    paddingHorizontal: 24,
  },
  connectButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
});

export default Supabase;
