import {useParams} from '@/root/web/routing.web';
import _ from 'lodash';
import React, { useMemo } from 'react';
import theme from '../../styles-prompt-to-app/theme';
import {useState} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, ScrollView, TextInput} from 'react-native';
import {useNavigate} from '@/root/web/routing.web';
import LeftSidebar from '../../dashboard/LeftSidebar';
import IntegrationBanner from '.././Components/IntegrationBanner';
import RelatedAgents from '.././Components/RelatedAgents';
import IntegrationDetail from '.././Components/IntegrationDetail';
import IntegrationHeader from '.././Components/IntegrationHeader';
import Tabs from '.././Components/Tabs';
import IntegrationPricing from '.././Components/IntegrationPricing';
import SupabaseConnectButton from '@/root/web/integrations/supabase';
import CustomModal from '../Components/CustomModal';
import { tr } from 'rn-dates';
import InAppPurchaseDetail from '@/root/web/integrations/InAppPurchase/components/InAppPurchaseDetail';
import IntegrationsApi from '@/root/web/api/IntegrationsApi';
import Analytics from '@/root/web/lib/segment';

const PLATFORM_TYPE = 'inAppPurchases';

const InAppPurchase = ({integration}: any) => {
  const navigate = useNavigate();

  const [activeTab, setActiveTab] = useState('details');
  const [showConfig, setShowConfig] = useState(false);
  const [showDetails, setShowDetails] = useState(true);
  const [showPricing, setShowPricing] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [showIntegrationBanner, setShowIntegrationBanner] = useState(true);
  const [ModalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isIntegrated, setIsIntegrated] = useState(false);
  const [subscriptionIdList, setSubscriptionIdList] = useState<string[]>([]);
  const [productIdList, setProductIdList] = useState<string[]>([]);
  const [consumableProductIdList, setConsumableProductIdList] = useState<string[]>([]);

  // Get appId from params (simulate useParams from routing.web)
  const params = useParams<{id?: string}>();
  const appId = params?.id || '';

  const fetchCredentials = async () => {
    setLoading(true);
    try {
      const integrationResponse: any = await IntegrationsApi.fetchAppIntegration(appId, PLATFORM_TYPE, true);
      const integrationData: any = integrationResponse.data;
      if (integrationData) {
        setSubscriptionIdList((integrationData.credentials?.subscriptionIds || '').split(',').filter((v: string) => v));
        setProductIdList((integrationData.credentials?.productIds || '').split(',').filter((v: string) => v));
        setConsumableProductIdList((integrationData.credentials?.consumableProductIds || '').split(',').filter((v: string) => v));
        if (
          integrationData.credentials?.subscriptionIds ||
          integrationData.credentials?.productIds ||
          integrationData.credentials?.consumableProductIds
        ) {
          setIsIntegrated(true);
          setShowConfig(true);
          setShowIntegrationBanner(false);
          setIsConnected(true);
          setActiveTab('setupConfig');
        } else {
          setIsIntegrated(false);
        }
      }
    } catch(e){

    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (appId) fetchCredentials();
  }, [appId]);

  const onActiveTabChange = (value: string) => {
    setActiveTab(value);
  };

  const onConnectIntegration = () => {
    setShowConfig(true);
    setShowIntegrationBanner(false);
    setIsConnected(true);
    setActiveTab('setupConfig');
    setModalVisible(true);
    Analytics.track('agent_enabled', {
      integration: integration.title,
    });
  };

 const  handleDisconnectIntegration = () => {
  setShowConfig(false);
  setShowIntegrationBanner(true);
  setIsConnected(false);
  setActiveTab('details');
  }

  return (
    <View style={{width: '100%', justifyContent: 'center', alignItems: 'center', height:'100vh'}}>
      <TouchableOpacity
        style={styles.backcontainer}
        onPress={() => {
          navigate('../');
        }}
        activeOpacity={0.7}>
        <View style={styles.content}>
          <Text style={styles.arrow}>←</Text>
          <Text style={styles.text}>Back</Text>
        </View>
      </TouchableOpacity>
      <View style={styles.integrationsSection}>
        <IntegrationHeader isLoading={loading} integration={integration} isConnected={isConnected} onClick={onConnectIntegration} />
      </View>
      <View style={{width: '80%', flex: 1}}>
       {isConnected && <Tabs
          IactiveTab={activeTab}
          onChange={onActiveTabChange}
          showConfig={showConfig}
          showPricing={showPricing}
          showDetails={showDetails}
        />}
        <ScrollView
          style={{ flex: 1}}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{justifyContent: 'center', alignItems: 'center'}}>
          {activeTab === 'details' && <IntegrationDetail integration={integration} />}
          {activeTab === 'pricing' && <IntegrationPricing integration={integration} />}
          {activeTab !== 'details' && activeTab !== 'pricing' && (
            <InAppPurchaseDetail
              isModal={false}
              onClose={() => setModalVisible(false)}
              integration={integration}
              setLoading={setLoading}
              isIntegrated={isIntegrated}
              subscriptionIdList={subscriptionIdList}
              setSubscriptionIdList={setSubscriptionIdList}
              productIdList={productIdList}
              setProductIdList={setProductIdList}
              consumableProductIdList={consumableProductIdList}
              setConsumableProductIdList={setConsumableProductIdList}
              handleDisconnectIntegraion={handleDisconnectIntegration}
            />
          )}
          {showIntegrationBanner && <IntegrationBanner onBannerClick={onConnectIntegration} integration={integration} />}
          <RelatedAgents integration={integration} />
        </ScrollView>
      </View>
      <CustomModal
        visible={ModalVisible}
        onClose={() => setModalVisible(false)}
        showCloseButton={true}
        backdropOpacity={0.7}
        closeOnBackdropPress={false}
        modalStyle={styles.customModalStyle}
        contentStyle={styles.customContentStyle}
      >
        <InAppPurchaseDetail
          onClose={() => setModalVisible(false)}
          integration={integration}
          setLoading={setLoading}
          isIntegrated={isIntegrated}
          subscriptionIdList={subscriptionIdList}
          setSubscriptionIdList={setSubscriptionIdList}
          productIdList={productIdList}
          setProductIdList={setProductIdList}
          consumableProductIdList={consumableProductIdList}
          setConsumableProductIdList={setConsumableProductIdList}
          handleDisconnectIntegraion={handleDisconnectIntegration}
          isModal={true}
        />
      </CustomModal>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    minHeight: '100vh',
  },
  integrationsSection: {
    width: '80%',
    alignSelf: 'center',
    justifyContent: 'center',
    alignContent: 'center',
    marginBottom: 'clamp(20px , 4vw, 40px)',
  },
  integrationCard: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginVertical: 12,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  integrationLogo: {
    width: 'clamp(40px, 8vw, 125px)',
    height: 'clamp(40px, 8vw, 125px)',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    marginRight: 16,
  },
  logoSymbol: {
    fontSize: 'clamp(10px, 2vw, 24px)',
  },
  integrationInfo: {
    flex: 1,
  },
  integrationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  integrationName: {
    fontSize: 'clamp(10px, 2vw, 24px)',
    fontWeight: '600',
    fontFamily: 'General Sans',
    color: '#EFEFEF',
    letterSpacing: 0.48,
  },
  customModalStyle: {
    // backgroundColor: '#1A1D20',
    // borderColor: '#3D3D3D',
    width:'54%',
   
    borderRadius:16
    // borderWidth: 1,
  },
  customContentStyle: {
    // padding: 24,
    borderRadius: 16
  },
  priceBadge: {
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 20,
    fontSize: 12,
    color: '#EFEFEF',
    fontFamily: 'General Sans',
    fontWeight: '500',
    letterSpacing: 0.32,
    marginHorizontal: 14,
  },
  priceBadgeFree: {
    backgroundColor: '#007AFF',
  },
  priceBadgePaid: {
    backgroundColor: '#007AFF',
  },
  priceBadgeComingSoon: {
    backgroundColor: '#000',
    borderWidth: 1,
    borderColor: '#4E4E4E',
  },
  integrationDescription: {
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    marginBottom: 12,
    colorL: '#EFEFEF',
    fontFamily: 'General Sans',
    fontWeight: '400',
    letterSpacing: 0.36,
  },

  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 'clamp(2px, 2vw, 4px)',
  },
  tag: {
    backgroundColor: '#3D3D3D',
    color: '#e2e8f0',
    paddingVertical: 'clamp(2px, 2vw, 6px)',
    paddingHorizontal: 'clamp(4px, 2vw, 6px)',
    borderRadius: 20,
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontWeight: '500',
    marginRight: 8,
    marginBottom: 4,
  },
  detailsButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontFamily: 'General Sans',
    fontWeight: '400',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    letterSpacing: 0.4,
  },
  activeStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#19D885',
    borderWidth: 1,
    borderRadius: 20,
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    paddingVertical: 'clamp(2px, 2vw, 4px)',
    paddingHorizontal: 'clamp(6px, 2vw, 12px)',
    marginHorizontal: 'clamp(6px, 2vw, 14px)',
  },
  dot: {
    width: 'clamp(4px, 2vw, 8px)',
    height: 'clamp(4px, 2vw, 8px)',
    borderRadius: 9,
    backgroundColor: '#19D885',
    marginRight: 'clamp(4px, 2vw, 8px)',
  },
  activeStatusText: {
    color: '#19D885',
    fontSize: 'clamp(4px, 2vw, 12px)',
    fontWeight: '500',
    letterSpacing: 0.32,
    fontFamily: 'General Sans',
  },
  backcontainer: {
    width: '5%',
    backgroundColor: 'transparent',
    paddingLeft: '10%',
    alignSelf: 'flex-start',
    marginVertical: 20,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrow: {
    fontSize: 18,
    color: '#EFEFEF',
    marginRight: 8,
    fontWeight: '400',
  },
  text: {
    fontSize: 16,
    color: '#EFEFEF',
    fontWeight: '400',
  },
  container: {
    flex: 1,
    // paddingHorizontal: 35,
    paddingVertical: 20,
    // alignItems: 'center',
    backgroundColor: '#121212',
    minHeight: '100vh',
    width: '100%',
    padding: 'clamp(20px, 5vw, 54px)',
  },
  containerwrap: {
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#121212',
  },
  container2: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  iconStyles: {
    width: 'clamp(60px, 8vw, 120px)',
    height: 'clamp(60px, 8vw, 120px)',
    aspectRatio: 1 / 1,
    borderRadius: 12,
    resizeMode: 'contain',
    borderWidth: 1,
    borderColor: '#3D3D3D',
  },
  leftSection: {
    marginRight: 16,
  },
  logo: {
    width: 60,
    height: 60,
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderWidth: 1,
    borderColor: '#3a3a3a',
  },
  logoIcon: {
    fontSize: 24,
    color: '#4ade80', // Green color for the arrow
  },
  middleSection: {
    flex: 1,
    marginRight: 16,
  },
  titleRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600' as const,
    color: 'white',
    marginRight: 12,
  },
  freeBadge: {
    backgroundColor: '#4285f4',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  freeBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500' as const,
  },
  description: {
    color: '#ccc',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  databaseTag: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: '#3a3a3a',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#4a4a4a',
    alignSelf: 'flex-start' as const,
  },
  databaseIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  databaseText: {
    color: '#ccc',
    fontSize: 12,
    fontWeight: '500' as const,
  },
  rightSection: {},
  connectButton: {
    backgroundColor: '#1060E0',
    paddingHorizontal: 48,
    paddingVertical: 12,
    borderRadius: 8,
  },
  connectButtonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: 'General Sans',
    color: '#FFFFFF',
  },
  tabsSection: {
    // flex: 1,
    display: 'flex',
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: 'clamp(10px, 2vw, 28px)',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#222528',
    borderRadius: 'clamp(5px, 2vw, 9px)',
  },
  tab: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 'clamp(6px, 2vw, 12px)',
    paddingHorizontal: 'clamp(8px, 2vw,24px)',
    borderRadius: 'clamp(4px, 2vw, 8px)',
    fontSize: 'clamp(10px, 2vw, 16px)',
    fontWeight: '500',
  },
  active: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabText: {
    color: '#EFEFEF',
    fontSize: 'clamp(8px, 2vw, 16px)',
    fontWeight: '500',
    letterSpacing: 0.32,
  },
  activeTabText: {
    color: '#EFEFEF',
  },
});

const InAppPurchaseModal= ({onClose}) => {
  return (
    <View style={stylesa.container}>
      <View style={stylesx.header}>
        <Text style={stylesx.title}>Configuration Fields</Text>
        <TouchableOpacity onPress={()=>{onClose()}} style={stylesx.closeButton}>
          <Text style={stylesx.closeButtonText}>×</Text>
        </TouchableOpacity>
      </View>

      <View style={stylesa.inputContainer}>
        <Text style={stylesa.label}>Subscription ID</Text>
        <TextInput style={stylesa.input} placeholder="Enter Subscription ID" />
        <TouchableOpacity style={stylesa.button}>
          <Text style={stylesa.buttonText}>Add</Text>
        </TouchableOpacity>
      </View>
      <AddedIds />
      <View style={stylesa.inputContainer}>
        <Text style={stylesa.label}>Product ID</Text>
        <TextInput style={stylesa.input} placeholder="Enter Product ID" />
        <TouchableOpacity style={stylesa.button}>
          <Text style={stylesa.buttonText}>Add</Text>
        </TouchableOpacity>
      </View>
      <AddedIds />

      <View style={stylesa.inputContainer}>
        <Text style={stylesa.label}>Consumable Product ID</Text>
        <TextInput style={stylesa.input} placeholder="Enter Consumable Product ID" />
        <TouchableOpacity style={stylesa.button}>
          <Text style={stylesa.buttonText}>Add</Text>
        </TouchableOpacity>
      </View>
      <AddedIds />
      <TouchableOpacity style={stylesa.saveButton}>
        <Text style={stylesa.saveButtonText}>Save</Text>
      </TouchableOpacity>
    </View>
  );
};

const AddedIds= () => {
  return (
    <View style={stylesf.container}>
      <Text style={stylesf.title}>Added IDs</Text>

      <View style={stylesf.itemContainer}>
        <Text style={stylesf.itemText}>Read / write all tables & views in the connected project</Text>
        <View style={stylesf.iconContainer}>
          <TouchableOpacity style={stylesf.iconButton}>{/* Here, place your edit icon */}</TouchableOpacity>
          <TouchableOpacity style={stylesf.iconButton}>{/* Here, place your copy icon */}</TouchableOpacity>
        </View>
      </View>

      <View style={stylesf.itemContainer}>
        <Text style={stylesf.itemText}>Read / write all tables & views in the connected project</Text>
        <View style={stylesf.iconContainer}>
          <TouchableOpacity style={stylesf.iconButton}>{/* Here, place your edit icon */}</TouchableOpacity>
          <TouchableOpacity style={stylesf.iconButton}>{/* Here, place your copy icon */}</TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const stylesa = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1D20',
    padding: 24,
    borderRadius: 12,
    margin: 10,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    color: '#ffffff',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  label: {
    color: '#ffffff',
    flex: 1,
  },
  input: {
    flex: 2,
    height: 40,
    borderColor: '#3D3D3D',
    borderWidth: 1,
    borderRadius: 10,
    backgroundColor: '#22262A',
    color: '#565656',
    paddingHorizontal: 10,
  },
  button: {
    backgroundColor: 'transparent',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'white',
    padding: 10,
    paddingHorizontal: 14,
    marginLeft: 10,
  },
  buttonText: {
    color: '#ffffff',
  },
  saveButton: {
    backgroundColor: '#007bff',
    borderRadius: 8,
    alignSelf: 'flex-end',
    width: '10%',
    padding: 6,
    marginTop: 12,
  },
  saveButtonText: {
    color: '#ffffff',
    textAlign: 'center',
    fontSize: 16,
  },
});


const stylesf = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#121212',
    borderRadius: 12,
    marginVertical: 10,
  },
  title: {
    fontSize: 14,
    color: '#FFFFFF',
    marginBottom: 16,
  },
  itemContainer: {
    backgroundColor: '#1A1D20',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemText: {
    color: '#B9B9B9',
    fontSize: 12,
  },
  iconContainer: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: 8,
  },
});

const stylesx = StyleSheet.create({
  modal: {
    backgroundColor: '#1C1C1E',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    alignSelf: 'center',
    // maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 20,
    fontFamily:'Genral Sans'
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 20,
  },
  subtitle: {
    fontSize: 12,
    color: '#9A9A9A',
    marginBottom: 16,
  },
  projectsContainer: {
    marginBottom: 16,
  },
  projectCard: {
    backgroundColor: '#161618',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#3D3D3D',
  },
  connectedCard: {
    backgroundColor: '#161618',
  },
  selectedCard: {
    borderWidth: 0,
    backgroundColor: '#34363E',
  },
  projectLeft: {
    flex: 1,
  },
  projectName: {
    color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 14,
    marginBottom: 4,
  },
  projectRegion: {
    fontSize: 12,
    color: '#9A9A9A',
  },
  regionText: {
    color: '#B0B0B0',
  },
  projectRight: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  connectedText: {
    color: '#3AB44A',
    fontWeight: '700',
    fontSize: 12,
    marginBottom: 6,
  },
  projectStatus: {
    fontSize: 12,
    color: '#9A9A9A',
  },
  statusText: {
    fontWeight: '700',
    color: '#B0B0B0',
  },
  createNewButton: {
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#6E6E7E',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  createNewButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  connectButton: {
    height: 40,
    backgroundColor: '#2563EB',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    paddingHorizontal: 24,
  },
  disonnectButton: {
    height: 40,
    backgroundColor: '#EB3223',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    paddingHorizontal: 24,
  },
  connectButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
});

export default InAppPurchase