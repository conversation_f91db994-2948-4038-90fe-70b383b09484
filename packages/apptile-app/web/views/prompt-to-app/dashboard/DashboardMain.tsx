/* eslint-disable react-native/no-inline-styles */
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {StyleSheet, Text, View, TextInput} from 'react-native';
import AppApi from '@/root/web/api/AppApi';
import AppApiV2 from '@/root/web/api/AppApiV2';
import {makeToast, removeToast} from '@/root/web/actions/toastActions';
import {IManifestResponse} from '@/root/web/api/ApiTypes';
import {useDispatch, useSelector} from 'react-redux';
import {deleteOrgApp} from '../../../actions/editorActions';
import {useNavigate, useParams} from '../../../routing.web';
import Button from '../../../components-v2/base/Button';
import {MaterialCommunityIcons} from 'apptile-core';
import {Dialog} from '@/root/web/layout-v2/SaveAndPublish';
import TextElement from '@/root/web/components-v2/base/TextElement';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import Fuse from 'fuse.js';
import Tooltip from '@/root/web/components-v2/base/SimpleTooltip';
import {Pressable as RNWebPressable, PressableProps as RNWebPressableProps} from 'react-native';
import CreateAppModal from '../../platform/app/CreateAppModal';
import {handleLogout} from '@/root/web/views/auth/authUtils';
import PromptDashboard from '../components/PromptDashboard';
import {useNavigation} from '@/root/web/contexts/NavigationContext';
import ModalComponent from '@/root/web/components-v2/base/Modal';
import IntegrationsPage from '../integrations/IntegrationsPage';
import NotificationsPage from '../notifications/NotificationsPage';
import AnalyticsPage from '../analytics/AnalyticsPage';
import {DashboardViewType} from '@/root/web/contexts/navigationReducer';
import AuthModal from '../components/AuthModal';
import UserProfile from './components/UserProfile';
import Header from './components/Header';
import Footer from './components/Footer';
import AppDeleteConfirmation from './components/AppDeleteConfirmation';

export type PressableProps = RNWebPressableProps & {
  onHoverIn: (e: MouseEvent) => void;

  onHoverOut: (e: MouseEvent) => void;
};
export function Pressable(props: PressableProps) {
  return <RNWebPressable {...props} />;
}

const DASHBOARD_FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  minMatchCharLength: 2,
  ignoreLocation: true,
  threshold: 0.1,
  keys: ['name'],
};

const DashboardMain: React.FC = ({}) => {
  const orgsById = useSelector((state: EditorRootState) => state.orgs.orgsById);
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);
  const {
    state: {currentView},
    navigateTo,
  } = useNavigation();

  const dispatch = useDispatch();
  let params = useParams();
  const orgId = params.orgId;
  const org = orgId && orgsById[orgId] ? orgsById[orgId] : null;

  const [showNewAppForm, setShowNewAppForm] = useState(false);
  const [isDeletionDialogVisible, setDeletionDialogVisible] = useState(false);
  const [deletableAppId, setDeletableAppId] = useState('');

  const [fuseSearch, setFuseSearch] = React.useState(new Fuse(Object.values([]), DASHBOARD_FUSE_OPTIONS));

  const [filterText, setFilterText] = useState('');
  const [filteredItems, setFilteredItems] = React.useState(fuseSearch?.search(filterText));
  const {userLoggedIn} = useSelector((state: EditorRootState) => state.user);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [popoverVisible, setPopoverVisible] = useState<{[key: string]: boolean}>({});
  const [editingAppId, setEditingAppId] = useState<string | null>(null);
  const [editingAppName, setEditingAppName] = useState('');
  const [originalAppName, setOriginalAppName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [appDates, setAppDates] = useState<Record<string, string>>({});
  const renameInputRef = useRef<TextInput>(null);

  const getAvatarColor = (appName: string) => {
    const colors = ['#4F46E5', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#06B6D4', '#EC4899', '#84CC16'];
    const index = appName.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const getAppInitial = (appName: string) => {
    return appName.charAt(0).toUpperCase();
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '—';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '—';

      return date.toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
    } catch (error) {
      return '—';
    }
  };

  const toggleMenu = (appId: string) => {
    setPopoverVisible(prev => ({
      ...prev,
      [appId]: !prev[appId],
    }));
  };

  const handlePopoverVisibilityChange = (appId: string, visible: boolean) => {
    setPopoverVisible(prev => ({
      ...prev,
      [appId]: visible,
    }));
  };

  const fetchAppDate = async (appUUID: string) => {
    try {
      const response = await AppApiV2.getManifest(appUUID);
      const manifest: IManifestResponse = response.data;

      setAppDates(prev => ({
        ...prev,
        [appUUID]: manifest.createdAt,
      }));
    } catch (error) {
      console.error('Failed to fetch app date for', appUUID, error);
      // Don't set any date if the API call fails
    }
  };

  const handleRenameApp = (appId: string) => {
    setPopoverVisible(prev => ({...prev, [appId]: false}));
    const app = appsById[appId];
    if (app) {
      setEditingAppId(appId);
      setEditingAppName(app.name);
      setOriginalAppName(app.name);
      setTimeout(() => {
        renameInputRef.current?.focus();
      }, 100);
    }
  };

  const handleRenameComplete = async (appId: string) => {
    if (isSaving || !editingAppName.trim()) {
      if (!editingAppName.trim()) {
        setEditingAppName(originalAppName);
      }
      setEditingAppId(null);
      return;
    }

    if (editingAppName === originalAppName) {
      setEditingAppId(null);
      return;
    }

    setIsSaving(true);

    // Show progress toast with unique ID
    const progressToastId = 'rename-progress-' + Date.now();
    dispatch(
      makeToast({
        id: progressToastId,
        content: 'Renaming app...',
        appearances: 'info',
      }),
    );

    try {
      await AppApi.updateBasicAppInfo(appId, {name: editingAppName});
      // Remove progress toast and show success
      dispatch(removeToast(progressToastId));
      dispatch(
        makeToast({
          content: 'App name updated successfully',
          appearances: 'success',
        }),
      );

      // Refresh app data
      dispatch({
        type: 'FETCH_ORGS',
      });
    } catch (error) {
      console.error('Failed to update app name:', error);
      setEditingAppName(originalAppName);

      // Remove progress toast and show error
      dispatch(removeToast(progressToastId));
      dispatch(
        makeToast({
          content: 'Failed to update app name',
          appearances: 'error',
        }),
      );
    } finally {
      setIsSaving(false);
      setEditingAppId(null);
    }
  };

  const handleDeleteApp = (appId: string) => {
    setPopoverVisible(prev => ({...prev, [appId]: false}));
    setDeletableAppId(appId);
    setDeletionDialogVisible(true);
  };

  useEffect(() => {
    const appsByOrg: any =
      orgId && orgsById[orgId]
        ? orgsById[orgId].apps.map(appId => {
            const app = appId && appsById[appId] ? appsById[appId] : null;
            return app;
          })
        : [];
    setFuseSearch(new Fuse(Object.values(appsByOrg), DASHBOARD_FUSE_OPTIONS));
  }, [orgId, orgsById, appsById]);

  useEffect(() => {
    const fi = fuseSearch.search(filterText);
    setFilteredItems(fi);
  }, [fuseSearch, filterText]);

  // Fetch app dates when the organization apps change
  useEffect(() => {
    if (org && org.apps) {
      org.apps.forEach(appId => {
        const app = appId && appsById[appId] ? appsById[appId] : null;
        if (app && !appDates[appId]) {
          // The appId is actually the UUID of the app
          fetchAppDate(appId);
        }
      });
    }
  }, [org, appsById, appDates]);
  const onEditPress = (appId: string) => {
    // navigate(`/dashboard/${orgId}/app/${appId}/dashboard/store`);
    window.location.assign(`/dashboard/${orgId}/app/${appId}`);
  };

  useEffect(() => {
    const hideIntercom = () => {
      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = 'none';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = 'none';
      }
    };

    // Initial attempt in case Intercom is already loaded
    hideIntercom();

    // Set up MutationObserver to watch for Intercom injection
    const observer = new MutationObserver(hideIntercom);
    observer.observe(document.body, {childList: true, subtree: true});

    // Cleanup
    return () => {
      observer.disconnect();
      // Show Intercom again
      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = '';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = '';
      }
    };
  }, []);

  const confirmAppDeletion = () => {
    if (isDeletionDialogVisible && deletableAppId) {
      onDeleteApp(deletableAppId);
      setDeletionDialogVisible(false);
      setDeletableAppId('');
    }
  };

  const onDeleteApp = (appId: string) => {
    if (orgId) dispatch(deleteOrgApp(orgId, appId));
  };

  const onCreateApp = useCallback(() => {
    setShowNewAppForm(true);
  }, []);

  const [textareaValue, setTextareaValue] = useState('');
  const textareaRef = useRef(null);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }, [textareaValue]);

  const handleTextareaChange = e => {
    setTextareaValue(e.target.value);
  };

  const onLogout = async () => {
    if (isLoggingOut) return;

    await handleLogout({
      redirectUrl: '/',
      setIsLoggingOut,
      onSuccess: () => console.log('Logout successful'),
    });
  };

  return (
    <>
      <View style={{}}>
        {currentView === DashboardViewType.INTEGRATIONS ? (
          <IntegrationsPage onBack={() => navigateTo(DashboardViewType.APPS)} />
        ) : currentView === DashboardViewType.NOTIFICATIONS ? (
          <NotificationsPage onBack={() => navigateTo(DashboardViewType.APPS)} />
        ) : currentView === DashboardViewType.ANALYTICS ? (
          <AnalyticsPage onBack={() => navigateTo(DashboardViewType.APPS)} />
        ) : (
          <>
            <div style={style.promptInboxConatinerStyle}>
              <div
                style={{
                  position: 'sticky',
                  top: 0,
                  zIndex: 100,
                  background: 'rgba(18,18,18,0.1)', // semi-transparent dark
                  backdropFilter: 'blur(8px)',
                  WebkitBackdropFilter: 'blur(8px)', // for Safari
                }}>
                <Header
                  isLoggedIn={userLoggedIn}
                  onLogout={onLogout}
                  setShowLoginModal={() => setShowLoginModal(true)}
                />
              </div>

            <div style={{position:'relative'}}>
            <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  zIndex: 0,
                  backgroundColor: '#121212',
                  height: '100dvh',
                  width: '100dvw',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="496" height="496" viewBox="0 0 496 496" fill="none">
                  <g filter="url(#filter0_fg_1775_5279)">
                    <circle cx="247.772" cy="247.921" r="147.772" fill="url(#paint0_radial_1775_5279)" />
                  </g>
                  <defs>
                    <filter
                      id="filter0_fg_1775_5279"
                      x="6.10352e-05"
                      y="0.148438"
                      width="495.544"
                      height="495.543"
                      filterUnits="userSpaceOnUse"
                      color-interpolation-filters="sRGB">
                      <feFlood flood-opacity="0" result="BackgroundImageFix" />
                      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                      <feGaussianBlur stdDeviation="40.358" result="effect1_foregroundBlur_1775_5279" />
                      <feTurbulence type="fractalNoise" baseFrequency="5 5" numOctaves="3" seed="5181" />
                      <feDisplacementMap
                        in="effect1_foregroundBlur_1775_5279"
                        scale="200"
                        xChannelSelector="R"
                        yChannelSelector="G"
                        result="displacedImage"
                        width="100%"
                        height="100%"
                      />
                      <feMerge result="effect2_texture_1775_5279">
                        <feMergeNode in="displacedImage" />
                      </feMerge>
                    </filter>
                    <radialGradient
                      id="paint0_radial_1775_5279"
                      cx="0"
                      cy="0"
                      r="1"
                      gradientUnits="userSpaceOnUse"
                      gradientTransform="translate(247.772 247.921) rotate(90) scale(147.772)">
                      <stop stop-color="#44BFFF" />
                      <stop offset="1" stop-color="#126ADD" />
                    </radialGradient>
                  </defs>
                </svg>
              </div>
              <div style={style.mainContentContainer}>
                <div
                  style={
                    {
                      // width: window.innerWidth <= 700 ? '98vw' : '',
                      // marginTop: '40px',
                    }
                  }>
                  <div
                    style={{
                      // marginBottom: '40px',
                      width: 'clamp(60vw, 70vw, 80vw)',
                      marginTop: window.innerWidth<=700 ? '200px': '100px',

                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <PromptDashboard />
                    {/* App List Section - Now uncommented and scrollable */}
                    {org && org.apps.length !== 0 && (
                      <div
                        style={{
                          // maxHeight: '40vh',
                          marginTop: 'clamp(60px, 6vw, 100px)',
                          paddingRight: 8,
                          display: 'flex',
                          flexDirection: 'column',
                          overflowX: 'hidden',
                          overflowY: 'auto',
                          scrollbarWidth: 'none',
                          msOverflowStyle: 'none',
                          backgroundColor: 'rgba(255, 255, 255, 0.05)',
                          backdropFilter: 'blur(17.4px)',
                          borderRadius: 20,
                          padding: 12,
                          paddingTop: 0,
                          borderWidth: 1,
                          borderStyle: 'solid',
                          borderColor: '#3D3D3D',
                          width: '90%',
                          marginBottom: 100,
                        }}>
                        <Text style={styles.titleText}>My apps</Text>
                        <View style={styles.appListContainer}>
                          {filterText.length === 0 &&
                            org &&
                            org.apps &&
                            org.apps.map((appId, index) => {
                              const app = appId && appsById[appId] ? appsById[appId] : null;
                              if (!app) return null;
                              return (
                                <div key={appId} style={styles.appCard}>
                                  <Pressable style={styles.appCardContent} onPress={() => onEditPress(appId)}>
                                    <div style={styles.appCardLeft}>
                                      <div
                                        style={{
                                          ...styles.appAvatar,
                                          backgroundColor: getAvatarColor(app.name),
                                        }}>
                                        <Text style={styles.avatarText}>{getAppInitial(app.name)}</Text>
                                      </div>
                                      <div style={styles.appInfo}>
                                        {editingAppId === appId ? (
                                          <TextInput
                                            ref={renameInputRef}
                                            style={styles.appNameInput}
                                            value={editingAppName}
                                            onChangeText={setEditingAppName}
                                            onBlur={() => handleRenameComplete(appId)}
                                            onSubmitEditing={() => handleRenameComplete(appId)}
                                            maxLength={100}
                                            selectTextOnFocus
                                            editable={!isSaving}
                                          />
                                        ) : (
                                          <Text style={styles.appName}>{app.name}</Text>
                                        )}
                                        {appDates[appId] && (
                                          <Text style={styles.appDate}>{formatDate(appDates[appId])}</Text>
                                        )}
                                      </div>
                                    </div>
                                  </Pressable>
                                  <div style={styles.appMenuContainer}>
                                    <PopoverComponent
                                      positions={['left']}
                                      visible={popoverVisible[appId] || false}
                                      propOnly={true}
                                      onVisibleChange={visible => handlePopoverVisibilityChange(appId, visible)}
                                      containerStyle={{
                                        marginTop: '-2.5rem',
                                        marginLeft: '2.5rem',
                                      }}
                                      trigger={
                                        <Pressable
                                          style={[styles.menuButton]}
                                          onPress={() => toggleMenu(appId)}
                                          onHoverIn={() => {}}
                                          onHoverOut={() => {}}>
                                          <Text style={styles.menuDots}>
                                            <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              width="24"
                                              height="24"
                                              viewBox="0 0 24 24"
                                              fill="none">
                                              <path
                                                d="M4.5 14.25C3.90326 14.25 3.33097 14.0129 2.90901 13.591C2.48705 13.169 2.25 12.5967 2.25 12C2.25 11.4033 2.48705 10.831 2.90901 10.409C3.33097 9.98705 3.90326 9.75 4.5 9.75C5.09674 9.75 5.66903 9.98705 6.09099 10.409C6.51295 10.831 6.75 11.4033 6.75 12C6.75 12.5967 6.51295 13.169 6.09099 13.591C5.66903 14.0129 5.09674 14.25 4.5 14.25ZM12 14.25C11.4033 14.25 10.831 14.0129 10.409 13.591C9.98705 13.169 9.75 12.5967 9.75 12C9.75 11.4033 9.98705 10.831 10.409 10.409C10.831 9.98705 11.4033 9.75 12 9.75C12.5967 9.75 13.169 9.98705 13.591 10.409C14.0129 10.831 14.25 11.4033 14.25 12C14.25 12.5967 14.0129 13.169 13.591 13.591C13.169 14.0129 12.5967 14.25 12 14.25ZM19.5 14.25C18.9033 14.25 18.331 14.0129 17.909 13.591C17.4871 13.169 17.25 12.5967 17.25 12C17.25 11.4033 17.4871 10.831 17.909 10.409C18.331 9.98705 18.9033 9.75 19.5 9.75C20.0967 9.75 20.669 9.98705 21.091 10.409C21.5129 10.831 21.75 11.4033 21.75 12C21.75 12.5967 21.5129 13.169 21.091 13.591C20.669 14.0129 20.0967 14.25 19.5 14.25Z"
                                                fill="white"
                                              />
                                            </svg>
                                          </Text>
                                        </Pressable>
                                      }>
                                      <View style={styles.dropdownMenu}>
                                        <Pressable
                                          style={styles.menuItem}
                                          onPress={() => handleRenameApp(appId)}
                                          onHoverIn={() => {}}
                                          onHoverOut={() => {}}>
                                          <MaterialCommunityIcons name="pencil-outline" size={16} color="#DEEEFF" />
                                          <Text style={styles.menuItemText}>Rename App</Text>
                                        </Pressable>
                                        <Pressable
                                          style={styles.menuItem}
                                          onPress={() => handleDeleteApp(appId)}
                                          onHoverIn={() => {}}
                                          onHoverOut={() => {}}>
                                          <MaterialCommunityIcons name="trash-can-outline" size={16} color="#EF4444" />
                                          <Text style={styles.deleteMenuText}>Delete App</Text>
                                        </Pressable>
                                      </View>
                                    </PopoverComponent>
                                  </div>
                                </div>
                              );
                            })}
                        </View>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <Footer/>
            </div>
          </>
        )}

        {isDeletionDialogVisible && (
        <AppDeleteConfirmation setDeletionDialogVisible={setDeletionDialogVisible} confirmAppDeletion={confirmAppDeletion} />
        )}
      </View>
      {showLoginModal && (
        <View style={styles.modalOverlay}>
          <ModalComponent
            visible={true}
            onVisibleChange={setShowLoginModal}
            content={<AuthModal onClose={() => setShowLoginModal(false)} />}
          />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000, // Ensure it appears on top of everything
    backgroundColor: 'transparent', // Let the modal handle its own background
  },
  container: {
    flexDirection: 'column',
    padding: 40,
    paddingTop: 20,
    backgroundColor: 'transparent', // Make transparent to show parallax background
    height: '100%',
    overflow: 'scroll',
    scrollbarColor: '#faebd736 #1f1f1f', // Gets rid of that little box in the bottom-right
    scrollbarWidth: 'thin',
    position: 'relative',
  },
  appWrapperBox: {
    flexDirection: 'row',
    marginTop: 30,
    maxHeight: 'calc(100vh - 500px)',
    width: '100%',
    paddingRight: 15,
    flexWrap: 'wrap',
    position: 'relative',
  },
  appHeader: {
    padding: 8,
    paddingBottom: 10,
    marginTop: 20,
    flexDirection: 'row',
  },
  appHeaderItem: {
    flex: 5,
    alignSelf: 'flex-end',
  },
  secondaryHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleText: {
    color: '#FAFAFA',
    fontFamily: 'General Sans',
    fontSize: 24,
    fontWeight: '500',
    lineHeight: '102%',
    letterSpacing: -0.6,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 12,
  },
  appHeaderAction: {
    flex: 1,
  },
  appBox: {
    width: '40%',
    flexShrink: 0,
    flexWrap: 'wrap',
    padding: 15,
    borderWidth: 1,
    borderColor: '#303030',
    borderRadius: 8,
    margin: 4,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#272727',
    transition: 'all 0.2s ease',
  },
  appBoxHovered: {
    backgroundColor: '#2F2F2F',
    transform: [{scale: 1.01}],
    borderColor: '#404040',
  },
  appBoxPressed: {
    backgroundColor: '#242424',
    transform: [{scale: 0.99}],
  },
  appTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#FAFAFA',
  },
  appActions: {
    flexDirection: 'row',
    alignSelf: 'flex-end',
  },
  iconStyle: {
    flex: 1,
    padding: 8,
    borderRadius: 50,
    backgroundColor: '#272727',
    marginLeft: 4,
    borderColor: '#404040',
    borderWidth: 1,
    width: 35,
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
    transition: 'all 0.2s ease',
    cursor: 'pointer',
  },
  actionText: {
    color: '#2096f3',
    textDecorationLine: 'underline',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#303030',
    backgroundColor: '#1F1F1F',
  },
  textBox: {
    padding: 10,
    paddingLeft: '7px',
    width: '100%',
    backgroundColor: '#121212',
    borderRadius: 10,
    color: '#FAFAFA',
  },
  noappsContainer: {
    width: '100%',
  },
  notFoundText: {
    fontSize: 14,
    color: '#909090',
    textAlign: 'center',
    paddingTop: 100,
  },
  appListContainer: {
    display: 'flex',
    flexDirection: 'column',
    // gap: 8,
    paddingHorizontal: 14,
    width: '100%',
  },
  appCard: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // width: '100%',
    height: 96,
    backgroundColor: 'rgba(0, 0, 0, 0.40)',
    borderRadius: 12,
    borderColor: '#3D3D3D',
    borderStyle: 'solid',
    borderWidth: 1,
    padding: '0 24px',
    position: 'relative',
    marginTop: 10,
    marginBottom: 10,
    cursor: 'pointer',
  },
  appCardHovered: {
    backgroundColor: '#2F3132',
    transform: [{scale: 1.01}],
  },
  appCardContent: {
    display: 'flex',
    flex: 1,
    alignItems: 'baseline',
    paddingLeft: 24,
    paddingRight: 24,
  },
  appCardLeft: {
    display: 'flex',
    alignItems: 'center',
    gap: 16,
  },
  appAvatar: {
    width: 50,
    height: 50,
    borderRadius: 9999,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '400',
    fontFamily: 'Inter',
  },
  appInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
  },
  appName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 'normal',
    fontFamily: 'Inter',
    fontStyle: 'normal',
  },
  appDate: {
    color: 'rgba(255, 255, 255, 0.70)',
    fontSize: 13,
    fontWeight: '300',
    lineHeight: 'normal',
    fontFamily: 'Inter',
    fontStyle: 'normal',
  },
  appMenuContainer: {
    position: 'relative',
  },
  menuButton: {
    padding: 12,
    borderRadius: 6,
    marginRight: 10,
    cursor: 'pointer',
  },
  menuDots: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 'normal',
  },
  dropdownMenu: {
    minWidth: 180,
    overflow: 'hidden',
    padding: '8px 0',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.10)',
    backgroundColor: 'rgba(152, 152, 152, 0.13)',
    backdropFilter: 'blur(7.5px)',
  },
  menuItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    cursor: 'pointer',
  },
  deleteMenuItem: {},
  menuItemText: {
    color: '#DEEEFF',
    fontFamily: 'General Sans',
    fontSize: 16,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 22.56,
    letterSpacing: -0.16,
    marginLeft: 12,
  },
  deleteMenuText: {
    color: '#DEEEFF',
    fontFamily: 'General Sans',
    fontSize: 16,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 22.56,
    letterSpacing: -0.16,
    marginLeft: 12,
  },
  appNameInput: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '400',
    fontFamily: 'Inter',
    fontStyle: 'normal',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    minHeight: 32,
  },
});

const style = {
  logoutButtonStyle: {
    background: 'transparent',
    border: '1.5px solid #1060E0',
    color: '#fff',
    borderRadius: '8px',
    padding: 'clamp(4px, 1vw, 6px) clamp(12px, 3vw, 18px)',
    fontSize: 'clamp(14px, 2vw, 18px)',
    cursor: 'pointer',
    fontFamily: 'DM Sans',
  },
  loginButtonStyle: {
    background: 'transparent',
    border: '1.5px solid #1060E0',
    color: '#fff',
    borderRadius: '8px',
    padding: 'clamp(4px, 1vw, 6px) clamp(12px, 3vw, 18px)',
    fontSize: 'clamp(14px, 2vw, 18px)',
    cursor: 'pointer',
    fontFamily: 'DM Sans',
  },
  getStartedButtonStyle: {
    background: '#007bff',
    border: 'none',
    color: '#fff',
    borderRadius: '8px',
    padding: 'clamp(4px, 1vw, 6px) clamp(12px, 3vw, 18px)',
    fontSize: 'clamp(14px, 2vw, 18px)',
    cursor: 'pointer',
    fontWeight: 500,
    fontFamily: 'DM Sans',
  },
  promptInboxConatinerStyle: {
    margin: 0,
    padding: 0,
    width: '100%',
    height: '100vh',
    fontFamily: 'sans-serif',
    overflowY: 'scroll',
    overflowX: 'hidden',
    backgroundColor: '#121212',
    scrollbarWidth: 'none',
    // backgroundImage: 'radial-gradient(circle, #4A4A4A 1px, transparent 1px)',
    backgroundSize: '20px 20px',
    maxHeight: '100vh',
    position:'relative'
  },
  myAppsContainer: {
    maxHeight: '40vh',
    marginTop: 24,
    paddingRight: 8,
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    overflowX: 'hidden',
    overflowY: 'auto',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
  },
  logoContainerStyle: {
    position: 'absolute',
    top: '28px',
    left: 0,
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 2,
    padding: '0 clamp(20px, 5vw, 40px)',
    boxSizing: 'border-box',
    height: '50px',
  },
  headerContainerStyle: {
    width: '100vw',
    height: '100vh',
    backgroundImage: 'radial-gradient(#0f0f0f 1px, transparent 1px)',
    backgroundSize: '16px 16px',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainContentContainer: {
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: 'white',
    textAlign: 'center',
    zIndex: 1,
    fontSize: 'clamp(16px, 2vw, 20px)',
  },
  backgroundBlur: {
    position: 'absolute',
    width: '90vmin',
    height: '90vmin',
    background: '#007bff',
    filter: 'blur(90px)',
    opacity: 0.5,
    transform: 'rotate(45deg)',
    zIndex: 0,
    borderRadius: 0,
  },
};

export default DashboardMain;
