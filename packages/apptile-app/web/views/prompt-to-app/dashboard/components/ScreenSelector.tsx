import React, {useState, useEffect} from 'react';
import {ScreenConfig, MaterialCommunityIcons, getNavigationContext} from 'apptile-core';
import _ from 'lodash';
import {StyleSheet, Pressable, Text, View} from 'react-native';
import Animated, {useAnimatedStyle, useSharedValue, withTiming, Easing} from 'react-native-reanimated';
import {useSelector, batch, useDispatch} from 'react-redux';
import Analytics from '@/root/web/lib/segment';
import theme from '../../styles-prompt-to-app/theme';
import {selectScreensInNav} from '../../../../selectors/EditorSelectors';
import PopoverComponent from '../../../../components-v2/base/Popover';
import {
  EDITOR_SELECTED_PAGE_TYPE,
  EDITOR_SELECT_NAV_COMPONENT,
  editorSetActiveAttachmentId,
  editorSetActiveAttachmentKey,
} from '../../../../actions/editorActions';

export function ScreenSelector() {
  const [showPopover, setShowPopover] = useState(false);
  const [hovered, setHovered] = useState(false);

  const dispatch = useDispatch();
  const context = getNavigationContext();
  const screens: ScreenConfig[] = useSelector(selectScreensInNav);
  const activeNavigation = useSelector(s => (s as any).activeNavigation);
  const activeScreen = screens?.filter((e: ScreenConfig) => e.screen == activeNavigation?.activePageId);
  const [width, setWidth] = useState<number | null>(null);

  const slideAnimationHeight = useSharedValue(0);
  const fadeEffect = useSharedValue(1);

  const popOverAnimation = useAnimatedStyle(() => ({
    height: slideAnimationHeight.value,
    opacity: fadeEffect.value,
  }));

  useEffect(() => {
    fadeEffect.value = withTiming(showPopover ? 1 : 0, {
      duration: 120,
      easing: Easing.ease,
    });
  }, [showPopover, fadeEffect]);

  useEffect(() => {
    slideAnimationHeight.value = withTiming(showPopover ? 198 : 0, {
      duration: 100,
      easing: Easing.ease,
    });
  }, [showPopover, slideAnimationHeight]);

  useEffect(() => {
    const handleResize = () => {
      const appCanvas = document.getElementById('app-canvas');
      if (appCanvas) {
        setWidth(appCanvas.offsetWidth * 0.8);
      }
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div 
      style={{
        ...styles.appContainerHeader,
        backgroundColor: hovered ? theme.HOVER_COLOR : theme.BACKGROUND
      }} 
      className="screen-selector-root"
    >
      {activeScreen.length > 0 ? (
        <PopoverComponent
          visible={showPopover}
          onVisibleChange={setShowPopover}
          trigger={
            <Pressable
              onHoverIn={() => setHovered(true)}
              onHoverOut={() => setHovered(false)}
              style={[
                styles.popoverPressableStyles,
              ]}
              onPress={() => {
                setShowPopover(!showPopover);
                Analytics.track('page_dropdown_click');
              }}>
              {/* <Text style={[commonStyles.baseText, styles.appContainerHeaderPreText]}>Page:</Text> */}
              <Text style={[styles.appContainerHeaderText, {flex: 1}]}>
                {_.capitalize(activeScreen[0].title || activeScreen[0].name || 'Loading')}
              </Text>
              <MaterialCommunityIcons name="chevron-down" size={16} color={theme.FOREGROUND} />
            </Pressable>
          }>
          {screens && (
            <Animated.View
              style={[
                styles.appContainerHeader,
                styles.appContainerHeaderPopover,
                width ? {width} : {},
                popOverAnimation,
              ]}>
              <View
                id="page-selector"
                style={{
                  flex: 1,
                  overflow: 'auto',
                  padding: 12,
                  scrollbarColor: 'transparent transparent',
                  width: '100%',
                  gap: theme.THIN_GAP + 2,
                }}>
                {screens?.map((s: ScreenConfig, index: number) => (
                  <View key={s.name + '_' + index}>
                    <div
                      className={
                        activeScreen.length && activeScreen[0].name === s.name ? '' : 'screen-selector-options'
                      }
                      style={{
                        ...styles.appPopoverScreenText,
                        ...(activeScreen.length && activeScreen[0].name === s.name ? {backgroundColor: '#31383E'} : {}),
                      }}
                      onClick={() => {
                        batch(() => {
                          dispatch(editorSetActiveAttachmentId(''));
                          dispatch(editorSetActiveAttachmentKey(''));
                          dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: s.name});
                          dispatch({type: EDITOR_SELECTED_PAGE_TYPE, payload: s.type});
                        });
                        context.navigate(s.name);
                        setShowPopover(false);
                      }}>
                      {/* <View style={{width: 20}}>
                        <MaterialCommunityIcons
                          name={activeScreen.length && activeScreen[0].name == s.name ? 'check' : 'blank'}
                          size={16}
                          color={theme.FOREGROUND}
                        />
                      </View> */}
                      <span
                        style={{
                          color: theme.FOREGROUND, 
                          fontFamily: 'General Sans', 
                          fontSize: 13, 
                          fontWeight: '500'
                        }}>
                        {_.capitalize(s.title || s.name)}
                      </span>
                    </div>
                  </View>
                ))}
              </View>
            </Animated.View>
          )}
        </PopoverComponent>
      ) : (
        <View style={{width: '100%', height: '100%'}}>
          <Text style={[styles.appContainerHeaderText]}>Loading</Text>
        </View>
      )}
    </div>
  );
}

const styles = StyleSheet.create({
  appContainerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    backgroundColor: '#121212',
    paddingLeft: 14,
    paddingRight: 14,
    zIndex: 2,
    minWidth: 131,
    height: 44,
  },
  popoverPressableStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%', // Ensure the Pressable takes full width of its container
  },
  appContainerHeaderText: {
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    color: theme.FOREGROUND,
    fontSize: 13,
    fontWeight: '500',
    lineHeight: 24.3, // 1.35em of 18px
    letterSpacing: 0.36, // 2% of 18px
    fontFamily: 'General Sans',
    paddingVertical: 8,
  },
  appContainerHeaderPopover: {
    zIndex: 1,
    // width: 200,
    flexDirection: 'column',
    alignItems: 'flex-start',
    height: 175,
    overflow: 'hidden',
    padding: 0,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    opacity: 1,
    marginTop: 10,
    borderRadius: 10,
    backgroundColor: '#121212',
  },
  appPopoverScreenText: {
    flexDirection: 'row',
    paddingVertical: 10,
    textAlign: 'left',
    padding: theme.PADDING_SMALL,
    borderRadius: theme.CORNER_RADIUS_HALF,
    cursor: 'pointer',
  },
});
