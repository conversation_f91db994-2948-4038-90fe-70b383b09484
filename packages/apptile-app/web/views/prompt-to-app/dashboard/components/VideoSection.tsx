'use client';

import type React from 'react';
import {useRef, useState} from 'react';
import CustomModal from '../../integrations/Components/CustomModal';
import videoM from '../../../../assets/images/videoModals.svg';

const VideoSection: React.FC = () => {
  const mobileView = window.innerWidth <= 700;
  const [showVideo, setShowVideo] = useState(false);

  const containerStyles: React.CSSProperties = {
    minHeight: '100%',
    width: '100%',
    backgroundColor: '#121212',
    color: '#ffffff',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    position: 'relative',
    // overflow: 'hidden',
    // marginBottom: 30,
  };

  const glowEffectStyles: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '1600px',
    height: '1000px',

    borderRadius: '50%',
    filter: 'blur(38px)',

    zIndex: 0,
  };

  return (
    <div style={containerStyles}>
      {/* <CustomModal
        modalStyle={{width: '100%', height: '100%', maxHeight: '100vh', borderColor: 'transparent', borderRadius: 20}}
        visible={showVideo}
        onClose={() => {
          setShowVideo(false);
        }}>
        <p
          style={{
            position: 'absolute',
            backgroundColor: 'white',
            padding: 10,
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'center',
            left: 10,
            zIndex: 100,
            top: 2,
            fontWeight: 600,
            color: 'black',
          }}
          onClick={() => setShowVideo(false)}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
              d="M17 6.99023H1M1 6.99023L7 0.990234M1 6.99023L7 12.9902"
              stroke="#000"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </p>
        <div style={{padding: '54.5% 0 0 0', position: 'relative'}}>
          <iframe
            src="https://player.vimeo.com/video/1097477198?h=68a9af562c&badge=0&autoplay=1&autopause=0&player_id=0&app_id=58479/embed"
            allow="autoplay; fullscreen; picture-in-picture"
            frameborder="0"
            style={{position: 'absolute', top: 0, left: 0, width: '100%', height: '98%'}}></iframe>
        </div>
    
        <script src="https://player.vimeo.com/api/player.js"></script>
      </CustomModal> */}

      {!showVideo ? (
        <div
          style={{
            position: 'relative',
            display: 'flex',
            height: 'clamp(200px 50vw, 700px)',
            width: 'clamp(250px 60vw, 1100px)',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <img src={videoM} alt="" height="100%" width="100%" style={{display: 'block'}} />
          <p
            onClick={() => setShowVideo(true)}
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              margin: 0,
              color: 'white',
              backgroundColor: 'rgba(255, 255, 255, 0.25)',
              backdropFilter: 'blur(4px)',
              padding: '8px 12px',
              borderRadius: '50%',
              cursor: 'pointer',
              zIndex: 1,
            }}>
            <svg xmlns="http://www.w3.org/2000/svg" width="54" height="54" viewBox="0 0 54 54" fill="none">
              <g filter="url(#filter0_d_1775_5772)">
                <path
                  d="M16.2908 13.3047C16.2908 12.9589 16.3733 12.6197 16.5295 12.3163C17.0427 11.3345 18.203 10.9797 19.1245 11.5264L42.0843 25.1104C42.3967 25.2956 42.6533 25.5701 42.8251 25.9025C43.3383 26.8843 43.0036 28.1226 42.0843 28.6671L19.1245 42.2511C18.8451 42.4197 18.5248 42.5084 18.1985 42.5077C17.1454 42.5077 16.2908 41.5973 16.2908 40.475V13.3047Z"
                  fill="white"
                />
              </g>
              <defs>
                <filter
                  id="filter0_d_1775_5772"
                  x="6.87956"
                  y="1.85832"
                  width="45.5976"
                  height="50.0607"
                  filterUnits="userSpaceOnUse"
                  color-interpolation-filters="sRGB">
                  <feFlood flood-opacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feOffset />
                  <feGaussianBlur stdDeviation="4.70561" />
                  <feComposite in2="hardAlpha" operator="out" />
                  <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
                  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1775_5772" />
                  <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1775_5772" result="shape" />
                </filter>
              </defs>
            </svg>
          </p>
          <p
            onClick={() => setShowVideo(true)}
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              margin: 0,
              color: 'white',
              // background:'rgba(0, 0, 0, 0.40)',
              // filter: 'blur(22px)',
              padding: '8px 12px',
              borderRadius: '50%',
              cursor: 'pointer',
            }}>
            <svg width="268" height="268" viewBox="0 0 268 268" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g filter="url(#filter0_f_1775_5769)">
                <rect
                  x="44.7507"
                  y="44.6328"
                  width="178.502"
                  height="178.502"
                  rx="89.2511"
                  fill="black"
                  fill-opacity="0.4"
                />
              </g>
              <defs>
                <filter
                  id="filter0_f_1775_5769"
                  x="0.358147"
                  y="0.240288"
                  width="267.287"
                  height="267.289"
                  filterUnits="userSpaceOnUse"
                  color-interpolation-filters="sRGB">
                  <feFlood flood-opacity="0" result="BackgroundImageFix" />
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                  <feGaussianBlur stdDeviation="22.1963" result="effect1_foregroundBlur_1775_5769" />
                </filter>
              </defs>
            </svg>
          </p>
        </div>
      ) : (
        <div
          style={{
            backdropFilter: 'blur(4px)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh', // or any fixed height depending on your layout
            position: 'relative',
          }}>
          <p
            style={{
              position: 'absolute',
              backgroundColor: 'white',
              padding: 10,
              borderRadius: '50%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
              left: 10,
              zIndex: 100,
              top: 30,
              fontWeight: 600,
              color: 'black',
            }}
            onClick={() => setShowVideo(false)}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path
                d="M17 6.99023H1M1 6.99023L7 0.990234M1 6.99023L7 12.9902"
                stroke="#000"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </p>
          <div
            style={{
              position: 'relative',
              width: '90%',
              borderRadius: 14,
              border: '8px solid rgb(200, 205, 213, 0.1)',
              paddingTop: '50%',
            }}>
            <div style={glowEffectStyles}>
              <svg xmlns="http://www.w3.org/2000/svg" width="1600" height="1000" viewBox="0 0 321 325" fill="none">
                <g filter="url(#filter0_fg_1775_7216)">
                  <ellipse cx="160.5" cy="162.5" rx="70" ry="72.5" fill="#1060E0" />
                </g>
                <defs>
                  <filter
                    id="filter0_fg_1775_7216"
                    x="0.5"
                    y="0"
                    width="320"
                    height="325"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="45" result="effect1_foregroundBlur_1775_7216" />
                    <feTurbulence
                      type="fractalNoise"
                      baseFrequency="3.7559659481048584 3.7559659481048584"
                      numOctaves="3"
                      seed="5596"
                    />
                    <feDisplacementMap
                      in="effect1_foregroundBlur_1775_7216"
                      scale="177.49537658691406"
                      xChannelSelector="R"
                      yChannelSelector="G"
                      result="displacedImage"
                    />
                    <feMerge result="effect2_texture_1775_7216">
                      <feMergeNode in="displacedImage" />
                    </feMerge>
                  </filter>
                </defs>
              </svg>
            </div>
            <iframe
              src="https://player.vimeo.com/video/1097883985?badge=0&autoPlay=1&amp;autopause=0&amp;player_id=0&amp;app_id=58479"
              frameborder="0"
              allow="autoplay; fullscreen; picture-in-picture; clipboard-write; encrypted-media; web-share"
              style={{position: 'absolute', top: 0, left: 0, width: '100%', height: '100%'}}
              title="Tile.dev"></iframe>
          </div>
          <script src="https://player.vimeo.com/api/player.js"></script>
        </div>
      )}
    </div>
  );
};

export default VideoSection;
