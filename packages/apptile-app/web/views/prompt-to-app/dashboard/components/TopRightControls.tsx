import React, {useState, useRef, useEffect} from 'react';
import Animated, {useAnimatedStyle, withTiming, Easing} from 'react-native-reanimated';
import {View, StyleSheet, Pressable, Text} from 'react-native';
import {useIsPreview, initApptileIsEditable, initApptileIsPreview, Icon} from 'apptile-core';
import {useDispatch} from 'react-redux';
import {saveAppState} from '@/root/web/actions/editorActions';
// import Analytics from '@/root/web/lib/segment';
import {useNavigate, useParams} from '../../../../routing.web';
import theme from '../../styles-prompt-to-app/theme';
import {apptileMinimal, ExitIcon, playBtn, saveFolder} from '../../editor/components/svgelems';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import Analytics from '@/root/web/lib/segment';

export function TopRightControls() {
  const DURATION = 500;
  const EASING = Easing.bezier(0.25, 0.1, 0.25, 1);
  const isPreview = useIsPreview();
  const dispatch = useDispatch();
  const params = useParams();
  const navigate = useNavigate();

  const [hoveredItem, setHoveredItem] = useState('');
  const [buildsExist, setBuildsExist] = useState(false);
  const [buildsLoading, setBuildsLoading] = useState(true);
  // const [controlsWidth, setControlsWidth] = useState(0);
  // const [exitWidth, setExitWidth] = useState(0);

  // const controlsRef = useRef(null);
  // const exitRef = useRef(null);

  const onSave = () => {
    dispatch(saveAppState(false, true));
  };

  const onPublish = () => {
    dispatch(saveAppState(true, true, 'Updated template'));
  };

  const editorPreviewClicked = () => {
    // Analytics.track('editor:editor_previewClicked');
    dispatch(initApptileIsPreview(!isPreview));
    dispatch(initApptileIsEditable(isPreview));
    Analytics.track('preview_clicked');
  };

  // Check for builds on component mount
  useEffect(() => {
    const checkBuilds = async () => {
      if (!params?.id) {
        setBuildsLoading(false);
        return;
      }

      try {
        const response = await BuildManagerApi.getBuildsV2(params.id);
        // Check if builds exist for Android or iOS
        const hasBuilds = response && response?.data?.length > 0;

        setBuildsExist(hasBuilds);
      } catch (error) {
        console.error('Failed to fetch builds:', error);
        // If API fails, default to showing "Publish" (buildsExist = true)
        setBuildsExist(true);
      } finally {
        setBuildsLoading(false);
      }
    };

    checkBuilds();
  }, [params?.id]);

  const handleLaunchClick = () => {
    if (buildsLoading) return;

    if (!buildsExist) {
      // No builds exist - show "Launch" behavior: trigger onPublish AND navigate
      onPublish();
      navigate(
        `/dashboard/${params?.orgId}/app/${params?.id}/f/${params?.forkId}/b/${params?.branchName}/dashboard/publish`,
      );
    } else {
      // Builds exist - show "Publish" behavior: only trigger onPublish
      onPublish();
    }
  };

  // const onControlsLayout = event => {
  //   const {width} = event.nativeEvent.layout;
  //   setControlsWidth(width);
  // };

  // const onExitLayout = event => {
  //   const {width} = event.nativeEvent.layout;
  //   setExitWidth(width);
  // };

  // const animatedStyle = useAnimatedStyle(() => {
  //   return {
  //     transform: [
  //       {
  //         translateX: withTiming(isPreview ? controlsWidth + 20 : 0, {
  //           duration: DURATION,
  //           easing: EASING,
  //         }),
  //       },
  //     ],
  //   };
  // });

  // const exitAnimatedStyle = useAnimatedStyle(() => {
  //   return {
  //     transform: [
  //       {
  //         translateX: withTiming(!isPreview ? 10 : -exitWidth, {
  //           duration: DURATION,
  //           easing: EASING,
  //         }),
  //       },
  //     ],
  //   };
  // });

  return (
    <View nativeID="topright-controls-root" style={styles.rightSection}>
      <div
        style={{
          ...styles.previewRefreshContainer,
          // display: isPreview ? "none" : "flex",
          transition: 'transform 0.2s',
          position: isPreview ? 'absolute' : 'static',
          transform: isPreview ? 'translateX(500px)' : 'none',
        }}>
        <Pressable
          onHoverIn={() => {
            setHoveredItem('savebtn');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          style={[
            styles.button,
            styles.buttonPrimary,
            {backgroundColor: hoveredItem === 'savebtn' ? theme.HOVER_COLOR : theme.BACKGROUND},
          ]}
          onPress={onSave}>
          {saveFolder}
          <Text style={styles.buttonText}>Save</Text>
        </Pressable>
        <Pressable
          onHoverIn={() => {
            setHoveredItem('previewbtn');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          style={[
            styles.button,
            styles.buttonPrimary,
            {backgroundColor: hoveredItem === 'previewbtn' ? theme.HOVER_COLOR : theme.BACKGROUND},
          ]}
          onPress={editorPreviewClicked}>
          {playBtn}
          <Text style={styles.buttonText}>Preview</Text>
        </Pressable>
        <Pressable
          onHoverIn={() => {
            setHoveredItem('publishbtn');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          style={[
            styles.button,
            styles.buttonPrimary,
            {
              backgroundColor: hoveredItem === 'publishbtn' ? '#006EE6' : theme.ACCENT,
            },
          ]}
          onPress={handleLaunchClick}>
          {apptileMinimal}
          <Text style={styles.buttonText}>{buildsLoading ? 'Loading...' : buildsExist ? 'Publish' : 'Launch'}</Text>
        </Pressable>
      </div>
      <div
        style={{
          ...styles.previewRefreshContainer,
          // display: isPreview ? "flex" : "none",
          transition: 'transform 0.2s',
          position: isPreview ? 'static' : 'absolute',
          transform: isPreview ? 'none' : 'translateX(500px)',
        }}>
        <Pressable
          onHoverIn={() => {
            setHoveredItem('exitbtn');
          }}
          onHoverOut={() => {
            setHoveredItem('');
          }}
          style={[
            styles.button,
            {
              backgroundColor: '#182232',
              borderWidth: 2,
              borderColor: theme.ACCENT,
              paddingVertical: theme.PADDING_SMALL,
              paddingHorizontal: theme.PADDING,
            },
            {backgroundColor: hoveredItem === 'exitbtn' ? theme.HOVER_COLOR : theme.BACKGROUND},
          ]}
          onPress={editorPreviewClicked}>
          <ExitIcon />
          <Text style={[styles.buttonText, {paddingRight: 0}]}>Exit</Text>
        </Pressable>
      </div>
    </View>
  );
}

const styles = StyleSheet.create({
  rightSection: {
    flexDirection: 'row',
    alignSelf: 'flex-end',
    gap: theme.THIN_GAP,
    marginBottom: theme.THIN_GAP,
    overflow: 'hidden', // This ensures elements don't show when moved off-screen
  },
  previewRefreshContainer: {
    display: 'flex',
    flexDirection: 'row',
    gap: theme.THIN_GAP,
    alignItems: 'center',
  },
  previewButtonHighlight: {
    width: 117,
    borderRadius: 8,
    backgroundColor: theme.BACKGROUND,
    borderColor: theme.ACCENT,
    borderWidth: 3,
  },
  button: {
    backgroundColor: theme.BACKGROUND,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.PADDING_SMALL,
    borderRadius: theme.CORNER_RADIUS_HALF,
  },
  buttonPrimary: {
    backgroundColor: theme.ACCENT,
    paddingVertical: theme.PADDING_SMALL,
    paddingHorizontal: theme.PADDING,
  },
  buttonText: {
    // color: theme.FOREGROUND,
    // paddingHorizontal: theme.PADDING,
    paddingLeft: theme.PADDING_SMALL,
    color: '#CCCED2',
  },
  buttonIcon: {
    color: theme.FOREGROUND,
    paddingLeft: theme.PADDING,
  },
  launchButtonWrapper: {
    width: 117,
    borderRadius: 8,
    backgroundColor: theme.BACKGROUND,
    borderWidth: 0,
  },
});
