"use client"

import type React from "react"
import { useRef, useState } from "react"
import IconRender from "../../integrations/Components/IconRender"

const Caraousel: React.FC = () => {
  const carouselRef = useRef(null);
  const mobileView= window.innerWidth <=700
  const mokeupSize= window.innerWidth<=700 ? 220:  540
  const TemplateSize= window.innerWidth<=700 ? 100:  300
  const templateMarginLeft = mobileView ? -90 : -200
  const templateMarginBottom = mobileView ? 0 : 20
  const scroll = (direction) => {
    const container = carouselRef.current;
    const scrollAmount = container.offsetWidth;
    container.scrollBy({
      left: direction === 'left' ? -scrollAmount : scrollAmount,
      behavior: 'smooth',
    });
  };
  const slides = [
    {
      id: 1,
      icon:<svg xmlns="http://www.w3.org/2000/svg" width="33" height="32" viewBox="0 0 33 32" fill="none">
      <circle cx="4.27191" cy="4.47797" r="1.02484" fill="white"/>
      <circle cx="4.27191" cy="4.47797" r="1.02484" fill="white"/>
      <circle cx="4.27191" cy="8.23578" r="1.02484" fill="white"/>
      <circle cx="4.27191" cy="11.9936" r="1.02484" fill="white"/>
      <circle cx="4.27191" cy="15.7514" r="1.02484" fill="white"/>
      <circle cx="4.27191" cy="19.5092" r="1.02484" fill="white"/>
      <circle cx="19.3027" cy="8.23578" r="1.02484" fill="white"/>
      <circle cx="19.3027" cy="11.9936" r="1.02484" fill="white"/>
      <circle cx="27.1601" cy="19.5092" r="1.02484" fill="white"/>
      <circle cx="19.3027" cy="15.7514" r="1.02484" fill="white"/>
      <circle cx="27.1599" cy="23.2709" r="1.02484" fill="white"/>
      <circle cx="11.7873" cy="23.2709" r="1.02484" fill="white"/>
      <circle cx="19.3029" cy="19.5092" r="1.02484" fill="white"/>
      <circle cx="23.4033" cy="27.3686" r="1.02484" fill="white"/>
      <circle cx="27.1599" cy="27.0288" r="1.02484" fill="white"/>
      <circle cx="11.7873" cy="27.0288" r="1.02484" fill="white"/>
      <circle cx="8.0307" cy="4.47797" r="1.02484" fill="white"/>
      <circle cx="11.7873" cy="4.47797" r="1.02484" fill="white"/>
      <circle cx="15.5461" cy="4.47797" r="1.02484" fill="white"/>
      <circle cx="8.0307" cy="8.23578" r="1.02484" fill="white"/>
      <circle cx="11.7873" cy="8.23578" r="1.02484" fill="white"/>
      <circle cx="23.4033" cy="11.9936" r="1.02484" fill="white"/>
      <circle cx="23.4033" cy="15.7514" r="1.02484" fill="white"/>
      <circle cx="15.5461" cy="8.23578" r="1.02484" fill="white"/>
      <circle cx="27.1599" cy="11.9936" r="1.02484" fill="white"/>
      <circle cx="27.1599" cy="15.7514" r="1.02484" fill="white"/>
      <circle cx="8.0307" cy="19.5092" r="1.02484" fill="white"/>
      <circle cx="11.7873" cy="19.5092" r="1.02484" fill="white"/>
      <circle cx="15.8876" cy="27.3686" r="1.02484" fill="white"/>
      <circle cx="15.5461" cy="19.5092" r="1.02484" fill="white"/>
      <circle cx="19.6445" cy="27.3686" r="1.02484" fill="white"/>
      <circle cx="19.3029" cy="4.47797" r="1.02484" fill="white"/>
    </svg>,
      title: "Chat",
      description: "Describe Your App Idea With Text Or Visual Prompts, And See AI Agents Bring It To Life.",
      mockupType: "health",
    },
    {
      id: 2,
      icon:<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
      <circle cx="19.8457" cy="25.1473" r="0.976175" transform="rotate(-135 19.8457 25.1473)" fill="#D9D9D9"/>
      <circle cx="17.334" cy="22.6317" r="0.976175" transform="rotate(-135 17.334 22.6317)" fill="#D9D9D9"/>
      <circle cx="14.8242" cy="20.1219" r="0.976175" transform="rotate(-135 14.8242 20.1219)" fill="#D9D9D9"/>
      <circle cx="12.3145" cy="17.6092" r="0.976175" transform="rotate(-135 12.3145 17.6092)" fill="#D9D9D9"/>
      <circle cx="9.79688" cy="15.0956" r="0.976175" transform="rotate(-135 9.79688 15.0956)" fill="#D9D9D9"/>
      <circle cx="9.85352" cy="20.0677" r="0.976175" transform="rotate(-135 9.85352 20.0677)" fill="#D9D9D9"/>
      <circle cx="7.34375" cy="22.577" r="0.976175" transform="rotate(-135 7.34375 22.577)" fill="#D9D9D9"/>
      <circle cx="7.33984" cy="17.5521" r="0.976175" transform="rotate(-135 7.33984 17.5521)" fill="#D9D9D9"/>
      <circle cx="7.2832" cy="12.5838" r="0.976175" transform="rotate(-135 7.2832 12.5838)" fill="#D9D9D9"/>
      <circle cx="22.332" cy="22.66" r="0.976175" transform="rotate(-135 22.332 22.66)" fill="#D9D9D9"/>
      <circle cx="19.8145" cy="20.1507" r="0.976175" transform="rotate(-135 19.8145 20.1507)" fill="#D9D9D9"/>
      <circle cx="17.3047" cy="17.639" r="0.976175" transform="rotate(-135 17.3047 17.639)" fill="#D9D9D9"/>
      <circle cx="14.7949" cy="15.1234" r="0.976175" transform="rotate(-135 14.7949 15.1234)" fill="#D9D9D9"/>
      <circle cx="12.2773" cy="12.6122" r="0.976175" transform="rotate(-135 12.2773 12.6122)" fill="#D9D9D9"/>
      <circle cx="9.76758" cy="10.1024" r="0.976175" transform="rotate(-135 9.76758 10.1024)" fill="#D9D9D9"/>
      <circle cx="7.36914" cy="7.70005" r="0.976175" transform="rotate(-135 7.36914 7.70005)" fill="#D9D9D9"/>
      <circle cx="24.8457" cy="20.1507" r="0.976175" transform="rotate(-135 24.8457 20.1507)" fill="#D9D9D9"/>
      <circle cx="22.332" cy="17.6371" r="0.976175" transform="rotate(-135 22.332 17.6371)" fill="#D9D9D9"/>
      <circle cx="19.8184" cy="15.1278" r="0.976175" transform="rotate(-135 19.8184 15.1278)" fill="#D9D9D9"/>
      <circle cx="17.3086" cy="12.6082" r="0.976175" transform="rotate(-135 17.3086 12.6082)" fill="#D9D9D9"/>
      <circle cx="19.8457" cy="10.0726" r="0.976175" transform="rotate(-135 19.8457 10.0726)" fill="#D9D9D9"/>
      <circle cx="22.3047" cy="7.61362" r="0.976175" transform="rotate(-135 22.3047 7.61362)" fill="#D9D9D9"/>
      <circle cx="14.7949" cy="10.099" r="0.976175" transform="rotate(-135 14.7949 10.099)" fill="#D9D9D9"/>
      <circle cx="17.334" cy="7.55503" r="0.976175" transform="rotate(-135 17.334 7.55503)" fill="#D9D9D9"/>
      <circle cx="12.2812" cy="7.5853" r="0.976175" transform="rotate(-135 12.2812 7.5853)" fill="#D9D9D9"/>
    </svg>,
      title: "Edit",
      description: "Edits apps faster with a powerful visual editor - rearrange layouts, fine-tune visuals and customise interactions",
      mockupType: "editor",
    },
    {
      id: 3,
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 27.2744)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 9.85693 27.2744)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 9.85693 23.0791)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 12.1636 27.2744)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 12.1636 23.0791)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 14.4741 27.2744)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 14.4741 23.0791)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 16.7847 27.2744)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 16.7847 23.0791)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 19.0874 27.2744)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 19.0874 23.0791)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 9.85693 6.29297)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 12.1636 6.29297)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 14.4741 6.29297)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 16.7847 6.29297)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 19.0874 6.29297)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 9.85693 4.19775)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 12.1636 4.19775)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 14.4741 4.19775)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 16.7847 4.19775)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 19.0874 4.19775)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 27.2744)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 25.1768)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 25.1768)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 23.0791)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 23.0791)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 20.979)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 20.979)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 18.8813)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 18.8813)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 16.7812)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 16.7812)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 14.686)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 14.686)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 12.5859)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 12.5859)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 10.4883)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 10.4883)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 8.39307)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 4.19775)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 8.39307)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 4.19775)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 7.54639 6.29297)" fill="white"/>
      <circle cx="0.629371" cy="0.629371" r="0.629371" transform="matrix(1 0 0 -1 21.4019 6.29297)" fill="white"/>
    </svg>,
      title: "Launch",
      description: "Publish directly to App Store & Play Store. Scale effortlessly with integrations, built-in tools, and more",
      mockupType: "launch",
    }

  ]

  const containerStyles: React.CSSProperties = {
    minHeight: '100%',
    width: '100%',
    backgroundColor: '#121212',
    color: '#ffffff',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    // position: "relative",
    overflow: 'hidden',
    marginBottom:30
  };
  const caraousel: React.CSSProperties = {
    display: 'flex',
    overflowX: 'auto',
    scrollSnapType: 'x mandatory',
    scrollBehavior: 'smooth',
    WebkitOverflowScrolling: 'touch',
    gap: 'clamp(16px, 5vw, 30px)',
    padding: '10px',
    scrollbarWidth:'none'
  };


  const backgroundPatternStyles: React.CSSProperties = {
    // position: "absolute",
    top: 0,
    right: 0,
    width: "50%",
    height: "100%",
    // backgroundImage: `radial-gradient(circle, rgba(59, 130, 246, 0.3) 1px, transparent 1px)`,
    backgroundSize: "30px 30px",
    opacity: 0.4,
  }
  const glowEffectStyles: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 'clamp(200px, 40vw, 500px)',
    height:'clamp(200px, 40vw, 500px)',

    borderRadius: '50%',
    filter: 'blur(38px)',

    zIndex: 0,
  };


  const headerStyles: React.CSSProperties = {
    padding: 'clamp(20px, 10vw, 100px) clamp(20px, 10vw, 80px) clamp(20px, 10vw, 60px) clamp(20px, 10vw, 80px)',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  };

  const titleSectionStyles: React.CSSProperties = {
    // maxWidth: "600px",
  }

  const mainTitleStyles: React.CSSProperties = {
    fontSize: "clamp(26px, 4vw, 50px)",
    color:'#FAFAFA',
    fontFamily:'General Sans',
    fontWeight: "500",
    margin: "0 0 10px 0",
    lineHeight: "1.1",
    letterSpacing:'-1px'
  }


  const navigationStyles: React.CSSProperties = {
    display: mobileView? "none": "flex",
    gap: "12px",
    alignItems: "center",
  }

  const navButtonStyles: React.CSSProperties = {
    width: "48px",
    height: "48px",
    borderRadius: "50%",
    backgroundColor: '#161819',
    border: "1px solid rgba(63, 63, 70, 0.5)",
    color: "#ffffff",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    fontSize: "18px",
    transition: "all 0.2s ease",
    backdropFilter: "blur(10px)",
  }


  const carouselItem: React.CSSProperties = {
    scrollSnapAlign: 'start',
    // padding: '62px 41px 41px 62px',
    padding: 'clamp(10px, 5vw, 62px) clamp(10px, 5vw, 42px) clamp(10px, 5vw, 42px) clamp(10px, 5vw, 62px)',
    flex: '0 0 auto',
    width: mobileView ? 'clamp(240px , 50vw, 300px)' : 'clamp(240px, 66vw, 1300px)',
    height:'clamp(380px, 40vw, 520px)',
    backgroundColor:'#1A1D2080',
    borderRadius: '20px',
    border: "1px solid rgba(63, 63, 70, 0.5)",
    display: 'flex',
    flexDirection: mobileView? 'column':'row',
    justifyContent: mobileView?  'space-between' :'center',
    alignItems: 'center',
    fontSize: '24px',
  };
  

  const carouselContainerStyles: React.CSSProperties = {
    padding: '0px clamp(20px, 10vw, 80px) clamp(12px, 4vw, 20px) clamp(20px, 10vw, 80px)',
    position: "relative",
  }

  const carouselWrapperStyles: React.CSSProperties = {
    // overflow: "hidden",
    borderRadius: "20px",
  }


  const handleNavHover = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.2)"
    e.currentTarget.style.borderColor = "rgba(59, 130, 246, 0.4)"
  }

  const handleNavLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.backgroundColor = "rgba(39, 39, 42, 0.8)"
    e.currentTarget.style.borderColor = "rgba(63, 63, 70, 0.5)"
  }

  return (
    <div style={containerStyles}>
      <div style={backgroundPatternStyles}></div>

      <div style={headerStyles}>
        <div style={titleSectionStyles}>
          <h1 style={mainTitleStyles}>Chat, Edit, Launch  <br/>choose your way of building</h1>
         
        </div>

        <div style={navigationStyles}>
          <button
            style={navButtonStyles}
            onClick={() => scroll('left')}
            onMouseEnter={handleNavHover}
            onMouseLeave={handleNavLeave}
            aria-label="Previous slide"
          >
            ←
          </button>
          <button
            style={navButtonStyles}
            onClick={() => scroll('right')}
            onMouseEnter={handleNavHover}
            onMouseLeave={handleNavLeave}
            aria-label="Next slide"
          >
            →
          </button>
        </div>
      </div>

      <div style={carouselContainerStyles}>
        <div style={carouselWrapperStyles}>
        <div className="carousel-wrapper">
     
     <div style={caraousel} ref={carouselRef}>
       {slides.map((item, index) => (
         <div style={carouselItem} key={index}>
          <div style={{width:mobileView ? '100%': '50%'}}>
           <div style={{display:'flex', justifyContent:'flex-start', alignItems:'center', gap:16}}>
            <p style={{width:'clamp(20px, 4vw , 32px)', height: 'clamp(20px, 4vw , 32px)',}}>{item.icon}</p>
            <p style={{
                color: '#FAFAFA',
                fontFamily: "General Sans",
                fontSize: 'clamp(20px, 4vw , 30px)',
                fontStyle: 'normal',
                fontWeight: 500,
                lineHeight: 'normal',
                margin:0
            }}>{item.title}</p>
           </div>
          <div>
            <p style={{  color: '#C8C8C8',
  fontFamily: 'General Sans',
  fontSize:'clamp(12px, 3vw , 20px)',
  fontStyle: 'normal',
  fontWeight: 400,
  lineHeight: 'normal',
  letterSpacing: '0.8px',
  maxWidth:400,
  margin:0,
  textTransform: 'capitalize',}}>{item.description}</p>
          </div>
          </div>
          <div style={{width: mobileView ? '100%' : '70%', display:'flex', justifyContent:'flex-start',alignItems:'center' }}>
          <div style={{position: 'relative'}}>
              <div style={glowEffectStyles}>
              <svg xmlns="http://www.w3.org/2000/svg" width='clamp(200px, 40vw, 500px)' height='clamp(200px, 40vw, 500px)'viewBox="0 0 518 519" fill="none">
  <g filter="url(#filter0_fg_1775_5959)">
    <ellipse cx="259.138" cy="254.671" rx="165.329" ry="170.374" fill="#1060E0"/>
  </g>
  <defs>
    <filter id="filter0_fg_1775_5959" x="0.8358" y="-8.6769" width="516.605" height="526.694" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="38.8095" result="effect1_foregroundBlur_1775_5959"/>
      <feTurbulence type="fractalNoise" baseFrequency="3.5852398872375488 3.5852398872375488" numOctaves="3" seed="5596"/>
      <feDisplacementMap in="effect1_foregroundBlur_1775_5959" scale="185.94754028320312" xChannelSelector="R" yChannelSelector="G" result="displacedImage" width="100%" height="100%"/>
      <feMerge result="effect2_texture_1775_5959">
        <feMergeNode in="displacedImage"/>
      </feMerge>
    </filter>
  </defs>
</svg>
              </div>
              <div style={{position: 'relative',justifyContent:'flex-start', alignItems:'flex-end', display:'flex'}}>
                
              {item.id===1 ? (
                 <> <IconRender zIndex={200} icon={'phone'} size={mokeupSize} />
                  <div style={{marginLeft:`${templateMarginLeft}px`, marginBottom:`${templateMarginBottom}px`}}>
                <IconRender zIndex={200} icon={'framer'} size={TemplateSize} />
                </div>
                </>
              ): item.id===2 ? (   <>
              <IconRender zIndex={200} icon={'phone'} size={mokeupSize} />
                <div style={{marginLeft:`${templateMarginLeft}px`, marginBottom:`${templateMarginBottom}px`}}>
                <IconRender zIndex={200} icon={'frame2'} size={TemplateSize} />
                </div></>) :  (<> <IconRender zIndex={200} icon={'mobile2'} size={mokeupSize} />
               <div style={{marginLeft:`${templateMarginLeft}px`, marginBottom:`${templateMarginBottom}px`}}>
               <IconRender zIndex={200} icon={'platform'} size={TemplateSize} />
               </div></>)}
              </div>
            </div>
          </div>
         </div>
       ))}
     </div>

   </div>
        </div>
      </div>
   
    </div>
  )
}

export default Caraousel

