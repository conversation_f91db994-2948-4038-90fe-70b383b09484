import type React from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import UserProfile from './UserProfile';
import {useState} from 'react';
import {handleLogout} from '@/root/web/views/auth/authUtils';
import {View} from 'react-native';
import ModalComponent from '@/root/web/components-v2/base/Modal';
import AuthModal from '../../components/AuthModal';
import { useNavigate } from '@/root/web/routing.web';
import Analytics from '@/root/web/lib/segment';


const Header: React.FC<any> = ({showLoginModal, setShowLoginModal}: any) => {
  const navigate = useNavigate();
  const handleIconHover = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.opacity = '0.8';
  };

  const handleIconLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.opacity = '1';
  };

  const handleUserButtonHover = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.backgroundColor = '#007AFF';
  };

  const handleUserButtonLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.currentTarget.style.backgroundColor = '#126ADD26';
  };
  const {userLoggedIn, user} = useSelector((state: EditorRootState) => state.user);
  const [isHovered, setIsHovered] = useState(false);
  const [isSignUpHovered, setIsSignUpHovered] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [title, setTitle] = useState('Log in');

  const onLogout = async () => {
    if (isLoggingOut) return;

    await handleLogout({
      redirectUrl: '/',
      setIsLoggingOut,
      onSuccess: () => console.log('Logout successful'),
    });
  };

  return (
    <header style={headerStyles}>
      <div style={logoSectionStyles}>
        <div onClick={()=>{navigate('/')}} style={logoIconStyles}>
          <img
            src={require('@/root/web/assets/images/TileLogo.svg')}
            alt="Tile Logo"
            style={{width: 'clamp(80px, 8vw, 100px)', height: 'clamp(30px, 5vw, 56px)'}}
          />
        </div>
      </div>

      <div style={rightSectionStyles}>
        {!userLoggedIn ? (
          <>
            <div style={{display: window.innerWidth <= 700 ? 'none' : 'flex', gap: 10}}>
              <button
                style={twitterIconStyles}
                onMouseEnter={handleIconHover}
                onMouseLeave={handleIconLeave}
                onClick={() => window.open('https://x.com/tile_dev', '_blank')}
                aria-label="Twitter">
                𝕏
              </button>

              <button
                style={discordIconStyles}
                onMouseEnter={handleIconHover}
                onMouseLeave={handleIconLeave}
                onClick={() => window.open('https://discord.gg/eNKHZMMB', '_blank')}
                aria-label="Discord">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                </svg>
              </button>

              <button
                style={redditIconStyles}
                onMouseEnter={handleIconHover}
                onMouseLeave={handleIconLeave}
                onClick={() => window.open('https://www.reddit.com/r/TileDev/', '_blank')}
                aria-label="Reddit">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z" />
                </svg>
              </button>
            </div>

            <button
              onClick={() => {
                setShowLoginModal(true);
                setTitle('Log In');
              }}
              style={{
                ...loginButtonStyle,
                background: isHovered ? '#121212' : '#126ADD26',
                color: isHovered ? '#fff' : loginButtonStyle.color,
                border: isHovered ? '1.5px solidrgb(57, 154, 207)' : loginButtonStyle.border,
                transition: 'background 0.2s, color 0.2s, border 0.2s',
                boxShadow: isHovered ? '0 2px 8px #44BFFF33' : undefined,
              }}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}>
              <p
                style={{
                  color: '#FAFAFA',
                  fontFamily: 'General Sans',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 500,
                  lineHeight: 'normal',
                  margin: 0,
                }}>
                Login
              </p>
            </button>
            <button
              onClick={() => {
                setShowLoginModal(true);
                setTitle('Get Started');
              }}
              style={{
                background: isSignUpHovered ? '#1053E5' : '#1060E0',
                border: 'none',
                color: '#fff',
                borderRadius: '10px',
                padding: 'clamp(4px, 1vw, 10px) clamp(8px, 3vw, 10px)',
                fontSize: 'clamp(14px, 2vw, 18px)',
                cursor: 'pointer',
                fontWeight: 500,
                fontFamily: 'General Sans',
                minWidth: '105px',
                boxShadow: isSignUpHovered ? '0 4px 8px #44BFFF33' : undefined,
                transition: 'background 0.2s, color 0.2s, box-shadow 0.2s',
              }}
              onMouseEnter={() => setIsSignUpHovered(true)}
              onMouseLeave={() => setIsSignUpHovered(false)}>
              <p
                style={{
                  color: '#FAFAFA',
                  fontFamily: 'General Sans',
                  fontSize: '16px',
                  fontStyle: 'normal',
                  fontWeight: 500,
                  lineHeight: 'normal',
                  margin: 0,
                }}>
                Sign Up
              </p>
            </button>
          </>
        ) : (
          <>
            <div style={{display: window.innerWidth <= 700 ? 'none' : 'flex', gap: 10}}>
              <button
                style={twitterIconStyles}
                onMouseEnter={handleIconHover}
                onMouseLeave={handleIconLeave}
                onClick={() => window.open('https://x.com/tile_dev', '_blank')}
                aria-label="Twitter">
                𝕏
              </button>

              <button
                style={discordIconStyles}
                onMouseEnter={handleIconHover}
                onMouseLeave={handleIconLeave}
                onClick={() => window.open('https://discord.gg/eNKHZMMB', '_blank')}
                aria-label="Discord">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                </svg>
              </button>

              <button
                style={redditIconStyles}
                onMouseEnter={handleIconHover}
                onMouseLeave={handleIconLeave}
                onClick={() => window.open('https://www.reddit.com/r/TileDev/', '_blank')}
                aria-label="Reddit">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z" />
                </svg>
              </button>
            </div>

            <UserProfile
              onLogout={() => {
                onLogout();
                Analytics.track('logout_clicked', {
                  user_email: user?.email,
                  user_id: user?.id,
                });
              }}
            />
          </>
        )}
      </div>
      {showLoginModal && (
        <View style={modalOverlay}>
          <ModalComponent
            visible={true}
            onVisibleChange={setShowLoginModal}
            content={<AuthModal title={title} onClose={() => setShowLoginModal(false)} />}
          />
        </View>
      )}
    </header>
  );
};

const modalOverlay: React.CSSProperties = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1000, // Ensure it appears on top of everything
  backgroundColor: 'transparent', // Let the modal handle its own background
};

const headerStyles: React.CSSProperties = {
  // backgroundColor: 'rgba(250, 250, 250, 0.1)',
  backgroundColor: '#12121201',
  color: '#ffffff',
  padding: 'clamp(4px, 2vw, 6px) clamp(12px, 3vw, 48px)',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  // borderBottom: "1px solid #333",
};

const logoSectionStyles: React.CSSProperties = {
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
};

const loginButtonStyle: React.CSSProperties = {
  background: '#126ADD26',
  border: '1.5px solid #1060E040',
  color: '#fff',
  borderRadius: '10px',
  padding: 'clamp(4px, 1vw, 10px) clamp(8px, 3vw, 10px)',
  fontSize: 'clamp(14px, 2vw, 18px)',
  cursor: 'pointer',
  fontFamily: 'General Sans',
  minWidth: '105px',
};

const logoIconStyles: React.CSSProperties = {
  // width: "32px",
  // height: "32px",
  // backgroundColor: "#4285f4",
  // borderRadius: "6px",
  // display: "flex",
  // alignItems: "center",
  // justifyContent: "center",
  // fontSize: "18px",
  // fontWeight: "bold",
  // color: "#ffffff",
  cursor:'pointer'
};

const logoTextStyles: React.CSSProperties = {
  fontSize: '20px',
  fontWeight: '600',
  color: '#FFFFFF',
  fontFamily: 'Anaheim',
  margin: '0',
  letterSpacing: '2px',
};

const rightSectionStyles: React.CSSProperties = {
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
};

const iconButtonStyles: React.CSSProperties = {
  width: '32px',
  height: '32px',
  borderRadius: '50%',
  border: 'none',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '16px',
  transition: 'opacity 0.2s ease',
};

const twitterIconStyles: React.CSSProperties = {
  ...iconButtonStyles,
  backgroundColor: '#000000',
  color: '#ffffff',
};

const discordIconStyles: React.CSSProperties = {
  ...iconButtonStyles,
  backgroundColor: '#5865f2',
  color: '#ffffff',
};

const redditIconStyles: React.CSSProperties = {
  ...iconButtonStyles,
  backgroundColor: '#ff4500',
  color: '#ffffff',
  marginRight: '15px',
};

const userButtonStyles: React.CSSProperties = {
  backgroundColor: '#126ADD26',
  color: '#ffffff',
  borderRadius: '8px',
  padding: '10px 18px',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  cursor: 'pointer',
  fontSize: 'clamp(14px, 2vw, 18px)',
  fontWeight: '500',
  borderWidth: 1,
  borderColor: '#1060E040',
  transition: 'background-color 0.2s ease',
};

const userAvatarStyles: React.CSSProperties = {
  width: '20px',
  height: '20px',
  backgroundColor: '#007AFF',
  color: '#DEEEFF',
  borderRadius: '20%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: 'clamp(12px, 2vw, 16px)',
  fontWeight: '400',
};

const dropdownArrowStyles: React.CSSProperties = {
  fontSize: '12px',
  marginLeft: '4px',
};

export default Header;
