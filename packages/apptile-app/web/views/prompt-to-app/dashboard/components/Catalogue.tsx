import Caraousel from './Caraousel';
import Features from './Features';
import Footer from './Footer';
import VideoSection from './VideoSection';

const Catalogue = () => {

  return (
    <div style={styles.container}>
        <div style={styles.row}>
        <div style={{...styles.sideColumn, ...{borderTopColor:'transparent'}}}>
          <div style={styles.sideColumnInner}>
            <div style={styles.cornerBoxBottomRight}></div>
          
          </div>
        </div>
        <div style={{...styles.middleColumn, ...{borderTopColor:'transparent'}}}>
          
          <div style={styles.middleColumnContent}>
        {/* <div style={{width:'120vw'}}></div> */}
          </div>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxBottomLeft}></div>
            <div style={styles.cornerBoxBottomRight}></div>
          </div>
       
        </div>
        <div style={{...styles.sideColumn, ...{borderTopColor:'transparent'}}}>
          <div style={{...styles.sideColumnInner, alignItems: 'flex-start'}}>
            <div style={styles.cornerBoxBottomLeft}></div>
           
          </div>
        </div>
      </div>
      <div style={styles.row}>
        <div style={styles.sideColumn}>
          <div style={styles.sideColumnInner}>
            <div style={styles.cornerBoxTopRight}></div>
            <div style={styles.cornerBoxBottomRight}></div>
          </div>
        </div>
        <div style={styles.middleColumn}>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxTopLeft}></div>
            <div style={styles.cornerBoxTopRight}></div>
          </div>
          <div style={styles.middleColumnContent}>
          <VideoSection/>
          </div>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxBottomLeft}></div>
            <div style={styles.cornerBoxBottomRight}></div>
          </div>
        </div>
        <div style={styles.sideColumn}>
          <div style={{...styles.sideColumnInner, alignItems: 'flex-start'}}>
            <div style={styles.cornerBoxTopLeft}></div>
            <div style={styles.cornerBoxBottomLeft}></div>
          </div>
        </div>
      </div>
      <div style={styles.row}>
        <div style={styles.sideColumn}>
          <div style={styles.sideColumnInner}>
            <div style={styles.cornerBoxTopRight}></div>
            <div style={styles.cornerBoxBottomRight}></div>
          </div>
        </div>
        <div style={styles.middleColumn}>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxTopLeft}></div>
            <div style={styles.cornerBoxTopRight}></div>
          </div>
          <div style={styles.middleColumnContent}>
         <Caraousel/>
          </div>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxBottomLeft}></div>
            <div style={styles.cornerBoxBottomRight}></div>
          </div>
        </div>
        <div style={styles.sideColumn}>
          <div style={{...styles.sideColumnInner, alignItems: 'flex-start'}}>
            <div style={styles.cornerBoxTopLeft}></div>
            <div style={styles.cornerBoxBottomLeft}></div>
          </div>
        </div>
      </div>
      <div style={styles.row2}>
        <div style={styles.sideColumn}>
          <div style={styles.sideColumnInner}>
            <div style={styles.cornerBoxTopRight}></div>
            <p style={{color: 'white'}}>
           
            </p>
            <div style={styles.cornerBoxBottomRight}></div>
          </div>
        </div>
        <div style={styles.middleColumn}>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxTopLeft}></div>
            <div style={styles.cornerBoxTopRight}></div>
          </div>
          <div style={styles.middleColumnContent}>
           <Features />
          </div>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxBottomLeft}></div>
            <div style={styles.cornerBoxBottomRight}></div>
          </div>
        </div>
        <div style={styles.sideColumn}>
          <div style={{...styles.sideColumnInner, alignItems: 'flex-start'}}>
            <div style={styles.cornerBoxTopLeft}></div>
            <div style={styles.cornerBoxBottomLeft}></div>
          </div>
        </div>
      </div>
      <div style={styles.row}>
        <div style={styles.sideColumn}>
          <div style={styles.sideColumnInner}>
            <div style={styles.cornerBoxTopRight}></div>
          
          </div>
        </div>
        <div style={styles.middleColumn}>
          <div style={styles.middleColumnHeaderFooter}>
            <div style={styles.cornerBoxTopLeft}></div>
            <div style={styles.cornerBoxTopRight}></div>
          </div>
          <div style={styles.middleColumnContent}>
         
          </div>
       
        </div>
        <div style={styles.sideColumn}>
          <div style={{...styles.sideColumnInner, alignItems: 'flex-start'}}>
            <div style={styles.cornerBoxTopLeft}></div>
           
          </div>
        </div>
      </div>

    </div>
  );
};
const borderColor = '#3D3D3D80';
const plusColor = '#717171';
const styles = {
  container: {
    backgroundColor: '#121212',
    minHeight:'80%'
  },
  container2: {
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
    padding: '16px',
    background: '#111',
    minHeight: '100vh',
  },
  line: {
    height: '4px',
    width: '100%',
    borderRadius: '2px',
  },
  row: {
    width: '100%',
  
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: '#121212',
  },
  row2: {
    width: '100%',
   
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: '#121212',
    minHeight:'100vh'
  },
  sideColumn: {
    width: '5%',
    borderTop: `1px solid ${borderColor}`,
   
    backgroundColor: '#121212',
  },
  sideColumnInner: {
    display: 'flex',
    height: '100%',
    flexDirection: 'column',
    backgroundColor: '#121212',
    alignItems: 'flex-end', // or 'flex-start' for the right column
    justifyContent: 'space-between',
      },
  cornerBoxTopRight: {
    width: 'clamp(4px, 1vw , 12px)',
    height:  'clamp(4px, 1vw , 12px)',
    borderTop: `2px solid ${plusColor}`,
    borderRight: `2px solid ${plusColor}`,
    float: 'right',
  },
  cornerBoxBottomRight: {
    width: 'clamp(4px, 1vw , 12px)',
    height: 'clamp(4px, 1vw , 12px)',
    borderBottom: `2px solid ${plusColor}`,
    borderRight: `2px solid ${plusColor}`,
    float: 'right',
  },
  cornerBoxTopLeft: {
    width: 'clamp(4px, 1vw , 12px)',
    height: 'clamp(4px, 1vw , 12px)',
    borderTop: `2px solid ${plusColor}`,
    borderLeft: `2px solid ${plusColor}`,
    float: 'right',
  },
  cornerBoxBottomLeft: {
    width:  'clamp(4px, 1vw , 12px)',
    height:  'clamp(4px, 1vw , 12px)',
    borderBottom: `2px solid ${plusColor}`,
    borderLeft: `2px solid ${plusColor}`,
    float: 'right',
  },
  middleColumn: {
    width: '90%',
    border: `1px solid ${borderColor}`,
    height: '100%',
    backgroundColor: '#121212',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    borderBottom:0,
    flex: 1,
  },
  middleColumnHeaderFooter: {
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: '#121212',
    justifyContent: 'space-between',
  },
  middleColumnContent: {
    display: 'flex',
    height: '100%',
    backgroundColor: '#121212',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
};
export default Catalogue;
