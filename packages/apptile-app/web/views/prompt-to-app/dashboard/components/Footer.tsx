import type React from 'react';

const Footer: React.FC = () => {
  const footerStyles: React.CSSProperties = {
    backgroundColor: '#121212',
    color: '#ffffff',
    justifyContent: 'flex-start',
    padding: '40px 60px 30px 60px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  };

  const containerStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: window.innerWidth <= 700 ? 'column' :'row',
    gap:  window.innerWidth <= 700 ?'40px': '120px',
    marginBottom: '40px',
    flexWrap: 'wrap',
  };

  const sectionStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
  };

  const headingStyles: React.CSSProperties = {
    fontSize: '16px',
    fontWeight: '500',
    margin: '0 0 8px 0',
    color: '#ffffff',
  };

  const linkStyles: React.CSSProperties = {
    color: '#C8C8C8',
    textDecoration: 'none',
    fontSize: '14px',
    lineHeight: '1.5',
    cursor: 'pointer',
    transition: 'color 0.2s ease',
  };

  const emailStyles: React.CSSProperties = {
    color: '#b0b0b0',
    fontSize: '14px',
    lineHeight: '1.5',
  };

  const copyrightStyles: React.CSSProperties = {
    color: '#FFFFFF',
    fontSize: '14px',
    margin: '0',
    paddingTop: '20px',
  };

  const handleLinkHover = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.currentTarget.style.color = '#ffffff';
  };

  const handleLinkLeave = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.currentTarget.style.color = '#b0b0b0';
  };

  return (
    <footer style={footerStyles}>
      <div style={containerStyles}>
        <div style={sectionStyles}>
          <h3 style={headingStyles}>Legal</h3>
          <a href="/terms-of-use" style={linkStyles} onMouseEnter={handleLinkHover} onMouseLeave={handleLinkLeave}>
            Terms of Service
          </a>
          <a href="/privacy-policy" style={linkStyles} onMouseEnter={handleLinkHover} onMouseLeave={handleLinkLeave}>
            Privacy Policy
          </a>
        </div>

        <div style={sectionStyles}>
          <h3 style={headingStyles}>Payment & Refunds</h3>
          <a href="/pricing" style={linkStyles} onMouseEnter={handleLinkHover} onMouseLeave={handleLinkLeave}>
            Pricing
          </a>
        </div>

        <div style={sectionStyles}>
          <h3 style={headingStyles}>Contact Us</h3>
          <div style={emailStyles}>
            <strong style={{color: '#EFEFEF', fontWeight: 500}}>Email:</strong>
            <a
              href="mailto:<EMAIL>"
              style={{
                color: '#EFEFEF',
                fontWeight: 400,
                marginLeft: 4,
                textDecoration: 'none',
                cursor: 'pointer',
              }}>
              <EMAIL>
            </a>
          </div>
        </div>
      </div>

      <div style={copyrightStyles}>© 2025 Apptile Inc. All rights reserved</div>
    </footer>
  );
};

export default Footer;
