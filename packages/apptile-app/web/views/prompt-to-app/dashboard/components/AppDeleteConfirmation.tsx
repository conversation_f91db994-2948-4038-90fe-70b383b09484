import React from 'react'
import {StyleSheet, View} from 'react-native';
import ModalComponent from '@/root/web/components-v2/base/Modal';

interface IAppDeleteConfirmationProps {
    setDeletionDialogVisible: (visible: boolean) => void;
    confirmAppDeletion: () => void;
}

const AppDeleteConfirmation = ({setDeletionDialogVisible, confirmAppDeletion}: IAppDeleteConfirmationProps) => {
  return (
    <div style={styles.modalOverlay}>
    <ModalComponent
      visible={true}
      onVisibleChange={setDeletionDialogVisible}
      content={
        <div style={styles.modalContent}>
          <div style={styles.closeButtonContainer}>
            <div onClick={() => setDeletionDialogVisible(false)} style={styles.closeButton}>
              <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                <path d="M2 2L13 13" stroke="white" stroke-width="2" stroke-linecap="round" />
                <path d="M13 2L2 13" stroke="white" stroke-width="2" stroke-linecap="round" />
              </svg>
            </div>
          </div>

          <p style={styles.title}>
            Delete App
          </p>

          <div style={styles.contentContainer}>
            <p style={styles.message}>
              Are you sure you want to delete this app?
              This action cannot be undone.
            </p>

            <div style={styles.buttonContainer}>
              <button
                onClick={() => setDeletionDialogVisible(false)}
                style={styles.cancelButton}>
                Cancel
              </button>
              <button
                onClick={confirmAppDeletion}
                style={styles.confirmButton}>
                Confirm
              </button>
            </div>
          </div>
        </div>
      }
    />
  </div>
  )
}

const styles = {
    modalOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1000,
      backgroundColor: 'transparent',
    },
    modalContent: {
      borderRadius: 20,
      border: '1px solid #828282',
      backgroundColor: '#1A1A1A',
      borderColor: '#828282',
      width: 400,
      height: 200,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
    },
    closeButtonContainer: {
      width: '100%',
      display: 'flex',
      justifyContent: 'flex-end',
      borderRadius: '50px',
      height: 'fit-content',
    },
    closeButton: {
      marginTop: 12,
      marginRight: 16,
      cursor: 'pointer',
    },
    title: {
      color: '#EFEFEF',
      fontFamily: 'General Sans',
      fontSize: 20,
      fontWeight: 500,
      margin: '0px',
      marginTop: '4px',
    },
    contentContainer: {
      width: '70%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: 16,
    },
    message: {
      color: '#EFEFEF',
      fontFamily: 'General Sans',
      fontSize: 14,
      textAlign: 'center',
      marginBottom: 16,
    },
    buttonContainer: {
      display: 'flex',
      gap: 10,
    },
    cancelButton: {
      width: '130px',
      height: 40,
      backgroundColor: '#2F2F2F',
      border: 'none',
      borderRadius: 8,
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: 500,
      cursor: 'pointer',
    },
    confirmButton: {
      width: '130px',
      height: 40,
      backgroundColor: '#1060E0',
      border: 'none',
      borderRadius: 8,
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: 500,
      cursor: 'pointer',
    },
} as const;

export default AppDeleteConfirmation