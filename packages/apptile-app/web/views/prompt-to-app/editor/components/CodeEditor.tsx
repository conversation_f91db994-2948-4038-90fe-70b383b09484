import React, {useEffect, useRef} from 'react';
import {StyleSheet, useWindowDimensions, View} from 'react-native';
import {useDispatch} from 'react-redux';
import {fetchAppBranchesWithScheduledOta} from '@/root/web/actions/editorActions';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import {PlatformState} from '@/root/web/store/PlatformReducer';
import {ApptileCanvasScaleContext, initApptileIsEditable, ThemeContainer, reloadNavigators} from 'apptile-core';
import {Resizable} from 're-resizable';
import Animated from 'react-native-reanimated';
import ApptileApp from '../../../../../app/ApptileApp';
import {EditorState} from '../../../../common/webDatatypes';
import {useParams} from '../../../../routing.web';
import theme from '../../styles-prompt-to-app/theme';
import Button from '@/root/web/components-v2/base/Button';
import {useNavigate} from '../../../../routing.web';
import {reloadExternalPlugins} from '@/root/app/plugins/initPlugins';
import Analytics from '@/root/web/lib/segment';

interface EditorProps {
  editor: EditorState;
  platform: PlatformState;
  changeAppConfig: (appId: string, orgId: string, forkId: string | number, branchName?: string) => void;
  orgs: any;
  fetchOrgs: () => void;
  saveAppState: (
    newSave: boolean,
    showToast: boolean,
    message?: string,
    backup?: boolean,
    force?: boolean,
    saveWithCacheWithoutPublishing?: boolean,
  ) => void;
  updateApp: (publishedAppSaveId: number) => void;
  editorCopyAction: () => void;
  editorPasteAction: (pasteString: string) => void;
  configureDatasources: (appId: string, forceUpdateSecrets: boolean) => void;
  clearDatasourcesCredentials: () => void;
  softRestartConfig: () => void;
  changeAppContextData: (appId: string) => void;
  initApptileTilesMode: (isTilesOnly: boolean) => void;
  params: Record<string, string>;
}

export const CANVAS_SCALE = 0.8;
const APP_CANVAS_DIV_WIDTH = 445;
const APP_CANVAS_DIV_HEIGHT = 888;
const APP_CANVAS_BORDER_WIDTH = 4;

const TileEditor: React.FC<EditorProps> = props => {
  const {height: windowHeight} = useWindowDimensions();
  const {changeAppConfig, fetchOrgs, params, softRestartConfig, changeAppContextData, initApptileTilesMode} = props;
  const dispatch = useDispatch();
  const elements = useRef<{
    leftPanel: null | HTMLDivElement;
    rightPanel: null | Resizable;
    appCanvas: null | Animated.View;
    hoveredBoundary: null | HTMLDivElement;
    selectedBoundary: null | HTMLDivElement;
    selectedLabel: null | HTMLDivElement;
    propHoverBoundaries: HTMLDivElement[];
  }>({
    leftPanel: null,
    rightPanel: null,
    appCanvas: null,
    hoveredBoundary: null,
    selectedBoundary: null,
    selectedLabel: null,
    propHoverBoundaries: [],
  }).current;
  const navigate = useNavigate();

  useMountEffect(() => {
    const appId = params?.id ?? 1;
    const orgId = params?.orgId;
    const forkId = params?.forkId;
    const branchName = params?.branchName;
    if (appId && orgId && forkId && branchName) {
      changeAppConfig(appId, orgId, forkId, branchName);
    }
    fetchOrgs();
    dispatch(initApptileIsEditable(false));
  });

  useEffect(() => {
    initApptileTilesMode(false);
  }, []);

  const appId = params?.id;
  useEffect(() => {
    if (appId) {
      changeAppContextData(appId);
    }
  }, [appId, changeAppContextData]);

  useEffect(() => {
    dispatch(fetchAppBranchesWithScheduledOta(appId as string, params?.forkId));
  }, []);

  // Function to handle message events from the code editor iframe
  useEffect(() => {
    const handleMessage = async (event: MessageEvent) => {
      if (event.data && event.data.type === 'CODE_EDITOR_SAVE_COMPLETE') {
        Analytics.track('tile_modified_code');
        console.log('Received save complete message from code editor:', event.data);

        const {appId, artefactType} = event.data;

        if (appId) {
          try {
            if (artefactType === 'navigators') {
              console.log(`Reloading navigators for app ${appId}`);
              await reloadNavigators(appId);
            } else {
              console.log(`Reloading plugins for app ${appId}`);
              await reloadExternalPlugins({uuid: appId});
            }

            console.log('Dispatching soft restart config');
            dispatch(softRestartConfig());
          } catch (error) {
            console.error('Error handling code editor save message:', error);
          }
        }
      }

      if (event.data && event.data.type === 'CODE_EDITOR_CLOSE') {
        navigate(
          `/dashboard/${params?.orgId}/app/${params?.id}/f/${params?.forkId}/b/${params?.branchName}/dashboard/editor`,
        );
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [dispatch, params]);

  return (
    <ThemeContainer>
      <title>Edit {params?.pluginName}</title>
      {/* <style>{`
         * {
          box-sizing: border-box;
         }
      `}</style> */}
      <div
        id="code-editor-container"
        style={{
          flexDirection: 'row',
          overflow: 'hidden',
          display: 'flex',
          backgroundColor: '#34363d',
          backgroundImage: `radial-gradient(circle, #4a4a4a 1px, transparent 1px)`,
          backgroundSize: '20px 20px',
          flex: 1,
          height: '100vh',
          flexBasis: 'auto',
        }}>
        <div style={{width: '100%', height: '100%', display: 'flex'}}>
          <iframe
            src={`${location.protocol}//${location.hostname}/code-editor.html?appId=${
              params?.id
            }&type=${'plugins'}&artefactName=${params?.pluginName}`}
            style={{width: '100%', height: '100%', border: 'none'}}
          />
        </div>
        <div
          id="appPreviewContainer"
          className="app-preview-animate-in"
          style={{
            display: 'flex',
            gridTemplateColumns: '1fr',
            zIndex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
          }}>
          <View style={{height: '100%', flex: 1, justifyContent: 'center', zIndex: 1}}>
            <Animated.View style={[styles.appContainer]} id="app-canvas" ref={el => (elements.appCanvas = el)}>
              <ApptileCanvasScaleContext.Provider value={1}>
                <View style={styles.appCanvas}>
                  <ApptileApp />
                </View>
              </ApptileCanvasScaleContext.Provider>
            </Animated.View>
          </View>
          <div style={{position: 'absolute', top: 10, right: 10}}>
            <Button
              variant="FILLED"
              color="DEFAULT"
              containerStyles={{
                borderRadius: theme.CORNER_RADIUS_HALF,
                backgroundColor: theme.BACKGROUND,
                borderColor: theme.BACKGROUND,
                zIndex: 100,
                padding: theme.PADDING,
              }}
              textStyles={{
                color: theme.ERROR_COLOR,
                fontFamily: 'General Sans',
                fontSize: 14,
                fontWeight: '500',
              }}
              onPress={() => {
                navigate(
                  window.history.length > 1
                    ? -1
                    : `/dashboard/${params?.orgId}/app/${params?.id}/f/${params?.forkId}/b/${params?.branchName}/dashboard/editor`,
                );
              }}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </ThemeContainer>
  );
};

const styles = StyleSheet.create({
  deviceBezel: {
    backgroundColor: 'transparent',
    shadowColor: 'black',
    shadowOffset: {width: 10, height: 10},
    shadowRadius: 50,
    shadowOpacity: 0.4,
    overflow: 'visible',
  },
  leftPanel: {
    flex: 1,
    // maxWidth: 280,
    minWidth: 280,
    flexDirection: 'column',
    shadowColor: '#000000',
    alignItems: 'stretch',
    alignContent: 'stretch',
    flexGrow: 1,
    shadowOffset: {
      width: 5,
      height: 0,
    },
    shadowRadius: 10,
    shadowOpacity: 0.3,
    height: '100%',
  },
  leftPanelTop: {
    height: 'auto',
    flex: 1,
    flexBasis: 'auto',
    flexGrow: 0,
  },
  leftPanelAdj: {
    height: 'auto',
    flex: 1,
    flexBasis: 'auto',
    flexGrow: 0,
    position: 'absolute',
    top: 20,
    right: -48,
    width: 48,
    maxWidth: 48,
  },
  rowContainer: {
    height: 'auto',
    flex: 1,
    flexDirection: 'row',
    flexBasis: 'auto',
    flexGrow: 0,
    alignItems: 'stretch',
    marginLeft: 0,
  },
  leftScrollContainer: {
    flex: 1,
    padding: 2,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    fontSize: 12,
    flexBasis: 'auto',
  },
  appContainer: {
    position: 'relative',
    width: APP_CANVAS_DIV_WIDTH + 2 * APP_CANVAS_BORDER_WIDTH,
    height: APP_CANVAS_DIV_HEIGHT + 2 * APP_CANVAS_BORDER_WIDTH,
    borderRadius: 50,
    flexGrow: 0,
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: 'rgba(0,0,0,0)',
    borderWidth: APP_CANVAS_BORDER_WIDTH,
    borderColor: 'black',
    transform: [{scale: CANVAS_SCALE}],
  },
  appCanvas: {
    top: 0,
    left: 0,
    width: APP_CANVAS_DIV_WIDTH,
    height: APP_CANVAS_DIV_HEIGHT,
    position: 'absolute',
    borderRadius: 45,
    overflow: 'hidden',
    flexGrow: 0,
    flexShrink: 0,
  },
  rightPanel: {
    flex: 1,
    // maxWidth: 375,
    minWidth: 375,
    height: '100%',
    flexDirection: 'column',
    shadowColor: '#000000',
    shadowOffset: {
      width: -5,
      height: 0,
    },
    shadowRadius: 10,
    shadowOpacity: 0.3,
  },
  ml4: {marginLeft: 4},
  navButton: {
    flex: 1,
    backgroundColor: 'rgb(33, 150, 243)',
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginBottom: 2,
    borderRadius: 5,
  },
  QRPopover: {
    backgroundColor: theme.TILE_BACKGROUND,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 15,
    borderWidth: 1,
  },
});
function withParams(Component: any) {
  return (props: JSX.IntrinsicAttributes) => <Component {...props} params={useParams()} />;
}

export default withParams(TileEditor);
