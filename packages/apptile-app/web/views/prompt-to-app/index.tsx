import ModalComponent from '@/root/web/components-v2/base/Modal';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {handleLogout} from '@/root/web/views/auth/authUtils';
import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';

import AuthModal from './components/AuthModal';
import TextBoxContent from './components/TextBoxContent';
import Catalogue from './dashboard/components/Catalogue';
import Footer from './dashboard/components/Footer';
import Header from './dashboard/components/Header';

const PromptToApp: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [title, setTitle] = useState('Login');
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const {userLoggedIn} = useSelector((state: EditorRootState) => state.user);

  const dropdownRef = useRef<View>(null);
  const dropdownTriggerRef = useRef<TouchableOpacity>(null);

  const orgsById = useSelector((state: EditorRootState) => state.orgs.orgsById);
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (showUserDropdown) {
        const dropdownElement = dropdownRef.current;
        const triggerElement = dropdownTriggerRef.current;

        let dropdownDomNode = null;
        let triggerDomNode = null;

        if (dropdownElement && 'getNode' in dropdownElement) {
          dropdownDomNode = dropdownElement.getNode();
        } else if (dropdownElement) {
          dropdownDomNode = dropdownElement;
        }

        if (triggerElement && 'getNode' in triggerElement) {
          triggerDomNode = triggerElement.getNode();
        } else if (triggerElement) {
          triggerDomNode = triggerElement;
        }

        if (
          dropdownDomNode &&
          triggerDomNode &&
          !dropdownDomNode.contains(event.target as Node) &&
          !triggerDomNode.contains(event.target as Node)
        ) {
          setShowUserDropdown(false);
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserDropdown]);

  const wasLoggedOutRef = useRef<boolean | null>(null);

  useEffect(() => {
    if (wasLoggedOutRef.current === null) {
      wasLoggedOutRef.current = !userLoggedIn;
    }
  }, [userLoggedIn]);

  useEffect(() => {
    const needsSetup = () => {
      if (wasLoggedOutRef.current !== true || !userLoggedIn) {
        return false;
      }

      const currentPluginUrl = localStorage.getItem('plugin-server-url');
      const currentVimSetting = localStorage.getItem('enablevim');

      return currentPluginUrl !== 'https://api.apptile.io/plugin-server' || currentVimSetting !== 'yes';
    };

    if (needsSetup()) {
      console.log('Setting up localStorage after login');

      localStorage.setItem('plugin-server-url', 'https://api.apptile.io/plugin-server');
      localStorage.setItem('enablevim', 'yes');

      wasLoggedOutRef.current = false;

      localStorage.setItem('setupComplete', 'true');

      console.log('Setup complete, refreshing page once');
    } else if (wasLoggedOutRef.current === true && userLoggedIn) {
      console.log('No localStorage setup needed');
      wasLoggedOutRef.current = false;
    }
  }, [userLoggedIn]);

  useEffect(() => {
    const storedPrompt = sessionStorage.getItem('pendingPrompt');
    if (storedPrompt && userLoggedIn) {
      console.log('Found stored prompt after login:', storedPrompt);

      setPrompt(storedPrompt);

      sessionStorage.removeItem('pendingPrompt');

      setTimeout(() => {
        const sendButton = document.querySelector('[data-testid="send-button"]');
        if (sendButton) {
          (sendButton as HTMLElement).click();
        }
      }, 500);
    }
  }, [userLoggedIn]);

  useEffect(() => {
    const hideIntercom = () => {
      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = 'none';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = 'none';
      }
    };

    hideIntercom();

    const observer = new MutationObserver(hideIntercom);
    observer.observe(document.body, {childList: true, subtree: true});

    return () => {
      observer.disconnect();

      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = '';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = '';
      }
    };
  }, []);

  const handlePromptChange = (value: string) => {
    setPrompt(value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setPrompt(suggestion);
  };

  const handlePromptSubmit = () => {
    if (!userLoggedIn) {
      setShowLoginModal(true);

      if (wasLoggedOutRef.current === null) {
        wasLoggedOutRef.current = true;
      }
    } else {
    }
  };

  const onLogout = async () => {
    if (isLoggingOut) return;

    setShowUserDropdown(false);

    await handleLogout({
      redirectUrl: '/',
      setIsLoggingOut,
      onSuccess: () => console.log('Logout successful'),
    });
  };

  return (
    <View>
     <>
     <div style={{position:'relative'}}>
      <div style={{overflow: 'scroll', position:'relative',  scrollbarWidth: 'none', maxHeight: '100vh', zIndex:0, backgroundColor: '#121212'}}>
        <div
          style={{
            position: 'sticky',
            top: 0,
            zIndex: 100,
            background: 'rgba(18,18,18,0.1)', // semi-transparent dark
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)', // for Safari
          }}>
          <Header
            showLoginModal={showLoginModal}
            setShowLoginModal={setShowLoginModal}
          />
        </div>
        <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          zIndex: -1,
          backgroundColor: '#121212',
          height: '100dvh',
          width: '100dvw',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <svg xmlns="http://www.w3.org/2000/svg" width="496" height="496" viewBox="0 0 496 496" fill="none">
          <g filter="url(#filter0_fg_1775_5279)">
            <circle cx="247.772" cy="247.921" r="147.772" fill="url(#paint0_radial_1775_5279)" />
          </g>
          <defs>
            <filter
              id="filter0_fg_1775_5279"
              x="6.10352e-05"
              y="0.148438"
              width="495.544"
              height="495.543"
              filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB">
              <feFlood flood-opacity="0" result="BackgroundImageFix" />
              <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
              <feGaussianBlur stdDeviation="40.358" result="effect1_foregroundBlur_1775_5279" />
              <feTurbulence type="fractalNoise" baseFrequency="5 5" numOctaves="3" seed="5181" />
              <feDisplacementMap
                in="effect1_foregroundBlur_1775_5279"
                scale="200"
                xChannelSelector="R"
                yChannelSelector="G"
                result="displacedImage"
                width="100%"
                height="100%"
              />
              <feMerge result="effect2_texture_1775_5279">
                <feMergeNode in="displacedImage" />
              </feMerge>
            </filter>
            <radialGradient
              id="paint0_radial_1775_5279"
              cx="0"
              cy="0"
              r="1"
              gradientUnits="userSpaceOnUse"
              gradientTransform="translate(247.772 247.921) rotate(90) scale(147.772)">
              <stop stop-color="#44BFFF" />
              <stop offset="1" stop-color="#126ADD" />
            </radialGradient>
          </defs>
        </svg>
      </div>
        <div
          style={{
            margin: 0,
            padding: 0,
            width: '100%',
            minHeight: '66vh',
            fontFamily: 'sans-serif',
            overflow: 'hidden',
            backgroundColor: 'transparent',
            backgroundSize: '20px 20px',
            display: 'flex',
            justifyContent: 'center',
            // alignItems: 'center',
            marginTop: window.innerWidth<=700 ? '200px': '100px',
          }}>
          <div
            style={{
              position: 'relative',
              color: 'white',
              textAlign: 'center',
              zIndex: 1,
              fontSize: 'clamp(16px, 2vw, 20px)',
            }}>
            <TextBoxContent
              prompt={prompt}
              handlePromptChange={handlePromptChange}
              handlePromptSubmit={handlePromptSubmit}
              handleSuggestionClick={handleSuggestionClick}
            />
          </div>
        </div>
        <Catalogue />       
        <Footer />
      </div>
    </div>
     </>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    minHeight: '100vh',
    flexDirection: 'row',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: 'transparent',
  },

  sidebar: {
    width: 250,
    backgroundColor: '#0F0F0F',
    height: '100vh',
    flexDirection: 'column',
    transition: 'width 0.2s ease',
    borderRightWidth: 1,
    borderRightColor: '#222',
  },
  sidebarCollapsed: {
    width: 50,
  },
  sidebarHeader: {
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  sidebarHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  collapseButton: {
    padding: 5,
  },
  sidebarContent: {
    flex: 1,
    paddingVertical: 20,
    paddingHorizontal: 16,
    overflowY: 'auto',
    overflowX: 'hidden',
  },
  sidebarSectionTitle: {
    color: '#AAAAAA',
    fontSize: 14,
    marginBottom: 12,
  },
  sidebarItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 8,
  },
  sidebarItemText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginLeft: 8,
  },
  spacer: {
    flex: 1,
    minHeight: 20,
  },
  sidebarFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#222',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  userDetails: {
    marginLeft: 10,
  },
  userEmail: {
    color: '#AAAAAA',
    fontSize: 12,
    maxWidth: 180,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },

  userDropdown: {
    position: 'absolute',
    top: -50,
    left: 0,
    width: 150,
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#333',
    overflow: 'visible',
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 5,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    justifyContent: 'center',
  },
  dropdownIcon: {
    marginRight: 8,
  },
  dropdownText: {
    color: '#FFFFFF',
    fontSize: 14,
  },

  mainContent: {
    flex: 1,
    backgroundColor: 'transparent',
    transition: 'margin-left 0.2s ease',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 1,
  },
  mainContentExpanded: {
    marginLeft: 0,
  },
  content: {
    width: '100%',
    maxWidth: 800,
    padding: 20,
    alignItems: 'center',
    position: 'relative',
    zIndex: 2,
  },
  title: {
    fontSize: 48,
    fontWeight: '600',
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    color: themeColors.EDITOR_ACCENT_COLOR,
    marginBottom: 40,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 32,
  },
  suggestionsContainer: {
    width: '100%',
  },
});

export default PromptToApp;
