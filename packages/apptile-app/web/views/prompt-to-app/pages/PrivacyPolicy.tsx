import React, {useState} from 'react';
import {ScrollView, View, Text, StyleSheet} from 'react-native';
import StaticPageHeader from '../components/StaticPageHeader';
import theme from '../styles-prompt-to-app/theme';
import Header from '../dashboard/components/Header';
import Footer from '../dashboard/components/Footer';

const PrivacyPolicy: React.FC = () => {
  const [showLoginModal, setShowLoginModal] = useState<boolean>(false);
  return (
    <div className="thin-scrollbar" style={styles.pageContainer}>
      <style>
        {`
        .thin-scrollbar {
          scrollbar-width: thin;
        }
      `}
      </style>
      <div
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 100,
          width: '100%',
          background: 'rgba(18,18,18,0.1)', // semi-transparent dark
          backdropFilter: 'blur(8px)',
          WebkitBackdropFilter: 'blur(8px)', // for Safari
        }}>
        <Header showLoginModal={showLoginModal} setShowLoginModal={setShowLoginModal} />
      </div>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.contentContainer}>
        <View style={styles.contentWrapper}>
          <Text style={styles.title}>Privacy Policy</Text>
          <Text style={styles.subtitle}>Last Updated: July 01, 2025</Text>

          <Text style={styles.heading}>1. Information We Collect</Text>
          <Text style={styles.paragraph}>
            We collect information that you provide directly to us, such as when you create an account, use our
            services, or communicate with us. This may include:
          </Text>
          <View style={styles.list}>
            <Text style={styles.listItem}>• Contact information (name, email address, etc.)</Text>
            <Text style={styles.listItem}>• Account credentials</Text>
            <Text style={styles.listItem}>• Content you create or upload</Text>
            <Text style={styles.listItem}>• Communication preferences</Text>
          </View>

          <Text style={styles.heading}>2. How We Use Your Information</Text>
          <Text style={styles.paragraph}>We use the information we collect to:</Text>
          <View style={styles.list}>
            <Text style={styles.listItem}>• Provide, maintain, and improve our services</Text>
            <Text style={styles.listItem}>• Process transactions and send related information</Text>
            <Text style={styles.listItem}>• Respond to your comments, questions, and requests</Text>
            <Text style={styles.listItem}>• Monitor and analyze trends, usage, and activities</Text>
            <Text style={styles.listItem}>• Detect, investigate, and prevent security incidents</Text>
          </View>

          <Text style={styles.heading}>3. Information Sharing</Text>
          <Text style={styles.paragraph}>
            We do not sell your personal information. We may share your information in the following circumstances:
          </Text>
          <View style={styles.list}>
            <Text style={styles.listItem}>• With service providers who perform services on our behalf</Text>
            <Text style={styles.listItem}>• When required by law or to protect rights and safety</Text>
            <Text style={styles.listItem}>• In connection with a merger, sale, or asset transfer</Text>
          </View>

          <Text style={styles.heading}>4. Data Security</Text>
          <Text style={styles.paragraph}>
            We implement appropriate technical and organizational measures to protect your personal information.
            However, no method of transmission over the internet or electronic storage is 100% secure.
          </Text>

          <Text style={styles.heading}>5. Your Rights</Text>
          <Text style={styles.paragraph}>
            Depending on your location, you may have certain rights regarding your personal information, including:
          </Text>
          <View style={styles.list}>
            <Text style={styles.listItem}>• Access to your personal information</Text>
            <Text style={styles.listItem}>• Correction of inaccurate information</Text>
            <Text style={styles.listItem}>• Deletion of your personal information</Text>
            <Text style={styles.listItem}>• Restriction or objection to processing</Text>
            <Text style={styles.listItem}>• Data portability</Text>
          </View>

          <Text style={styles.heading}>6. Changes to This Policy</Text>
          <Text style={styles.paragraph}>
            We may update this Privacy Policy from time to time. We will notify you of any changes by updating the "Last
            Updated" date at the top of this page.
          </Text>

          <Text style={styles.heading}>7. Contact Us</Text>
          <Text style={styles.paragraph}>
            If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
          </Text>
        </View>
        <Footer />
      </ScrollView>
    </div>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
    overflowX: 'scroll',
    backgroundColor: '#000000',
    height: '100vh',
    maxHeight: '100vh',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    // paddingHorizontal: 24,
    // paddingVertical: 20,
    // paddingBottom: 40,
  },
  contentWrapper: {
    width: '80%',
    alignSelf: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF', // White text
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF', // White text
    marginBottom: 24,
    textAlign: 'center',
  },
  heading: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF', // White text
    marginTop: 20,
    marginBottom: 10,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: '#FFFFFF', // White text
    marginBottom: 15,
    textAlign: 'justify',
  },
  list: {
    marginLeft: 16,
    marginBottom: 16,
  },
  listItem: {
    fontSize: 16,
    lineHeight: 24,
    color: '#FFFFFF', // White text
    marginBottom: 5,
  },
});

export default PrivacyPolicy;
