import {useState} from 'react';
import FAQSection from './FAQ';
import Header from '../dashboard/components/Header';
import {useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {filter} from 'lodash';
import { useNavigate } from '@/root/web/routing.web';
import Footer from '../dashboard/components/Footer';

const Pricing = () => {
  const navigate = useNavigate();
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [hoveredButton, setHoveredButton] = useState<number | null>(null);
  const {userLoggedIn} = useSelector((state: EditorRootState) => state.user);
  const [showLoginModal, setShowLoginModal] = useState<boolean>(false);
  const [selectedCredits, setSelectedCredits] = useState('1000');
  const creditCostMap: Record<string, string> = {
    '1000': '$29',
    '2000': '$58',
    '3000': '$87',
    '4000': '$116',
    '5000': '$145',
  };

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    setSelectedCredits(value);
  };

  const handleButtonClick = () => {
   !userLoggedIn && setShowLoginModal(true);
  };
  const containerStyle = {
    background: 'radial-gradient(circle at center, #0A3A85 0%,rgb(0, 0, 0) 50%)',
    color: 'white',
    borderRadius: 20,
    backDropFilter: 'blur(40px)',
    width: window.innerWidth <= 700 ? 'none' : '80%',
    padding: '20px 20px',
    margin: window.innerWidth <= 700 ? 'none' : '0 auto', // centers horizontally
    marginTop: 20,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  };
  const rootContainer = {
    backgroundColor: '#000000',
    color: 'white',
    // minHeight: "100vh",
    height: '100vh',
    overflowX: 'scroll',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    scrollbarWidth: 'thin',
  };
  const textStyle = {
    color: '#C5C1BA',
    fontFamily: 'General Sans',
    fontSize: '13.563px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '21px',
  };

  const headerStyle = {
    textAlign: 'center' as const,
    marginBottom: '30px',
    // marginBottom: "30px",
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  };

  const logoStyle = {
    // marginBottom: "20px",
  };

  const logoIconStyle = {
    width: '50px',
    height: '50px',
    // background: "linear-gradient(135deg, #4285f4, #1a73e8)",
    borderRadius: '12px',
    margin: '0 auto',
    position: 'relative' as const,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  const logoIconAfterStyle = {
    width: '0',
    height: '0',
    borderLeft: '8px solid white',
    borderTop: '6px solid transparent',
    borderBottom: '6px solid transparent',
    marginLeft: '2px',
  };

  const titleStyle = {
    color: '#FCFBF8',
    textAlign: 'center',
    fontFamily: 'General Sans',
    fontSize: '34px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: 'normal',
  };

  const subtitleStyle = {
    color: '#C5C1BA',
    textAlign: 'center',
    fontFamily: 'General Sans',
    fontSize: '15.375px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '18px',
  };

  const cardsContainerStyle = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
    gap: '24px',
    maxWidth: '1200px',
    width: '100%',
  };
  const cardWrapper = {
    padding: '10px 20px',
    // background: "radial-gradient(circle at center, #1060E040 0%, rgba(10, 10, 10, 1) 80%)",
    display: 'flex',
    justifyContent: 'center',
  };

  const getCardStyle = (index: number, isPopular = false) => ({
    background: 'rgb(0,0,0,0.2)',
    // border:  `1px solid #5C5C5C`,
    border: `1px solid ${hoveredCard === index ? '#4285f4' : ' #5C5C5C'}`,
    borderRadius: '16px',
    padding: '16px',
    position: 'relative' as const,
    transition: 'transform 0.2s ease, border-color 0.2s ease',
    transform: hoveredCard === index ? 'translateY(-4px)' : 'translateY(0)',
  });

  const popularBadgeStyle = {
    position: 'absolute' as const,
    top: '-12px',
    left: '50%',
    transform: 'translateX(-50%)',
    background: '#4285f4',
    color: 'white',
    padding: '6px 16px',
    borderRadius: '20px',
    fontSize: '12px',
    fontWeight: '600',
    letterSpacing: '0.5px',
  };

  const cardHeaderStyle = {
    marginBottom: '12px',
  };

  const planNameStyle = {
    color: '#FCFBF8',
    fontFamily: 'General Sans',
    fontSize: '19.844px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '30px',
  };

  const priceStyle = {
    marginBottom: '12px',
  };

  const priceAmountStyle = {
    color: '#FCFBF8',
    fontFamily: 'General Sans',
    fontSize: '28px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '45px',
  };

  const pricePeriodStyle = {
    fontSize: '18px',
    color: '#888',
    marginLeft: '4px',
  };

  const planDescriptionStyle = {
    color: '#C5C1BA',
    fontFamily: 'General Sans',
    fontSize: '13px',
    fontStyle: 'normal',
    fontWeight: 500,
    margin: 0,
    padding: 0,
    lineHeight: '21px',
  };

  const creditsSectionStyle = {
    marginBottom: '32px',
  };
  const selectBoxStyle: React.CSSProperties = {
    background: '#1A1D20',
    border: '1px solid #444',
    borderRadius: '8px',
    padding: '12px 16px',
    fontSize: '14px',
    color: 'white',
    cursor: 'pointer',
    width: '100%',
    appearance: 'none',
    WebkitAppearance: 'none',
    MozAppearance: 'none',
    outline: 'none',
    fontFamily: 'General Sans',
    lineHeight: '21px',
  };

  const selectWrapperStyle: React.CSSProperties = {
    position: 'relative',
  };

  const selectArrowStyle: React.CSSProperties = {
    position: 'absolute',
    right: '16px',
    top: '50%',
    transform: 'translateY(-50%)',
    pointerEvents: 'none',
  };

  const creditsDropdownStyle = {
    background: '#1A1D20',
    border: '1px solid #444',
    borderRadius: '8px',
    padding: '12px 16px',
    fontSize: '14px',
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'space-between',
    transition: 'border-color 0.2s ease',
  };

  const creditsInfoStyle = {
    background: '#1A1D20',
    border: '1px solid #444',
    borderRadius: '8px',
    padding: '12px 16px',
    fontSize: '14px',
    color: 'white',
  };

  const getButtonStyle = (index: number, isPrimary = true) => ({
    color: '#F0F6FF',
    textAlign: 'center',
    fontFamily: 'General Sans',
    fontSize: '13.344px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '21px',
    width: '100%',
    padding: '12px 14px',
    borderRadius: '8px',

    border: isPrimary ? 'none' : '1px solid #FCFBF8',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    marginBottom: '4px',
    background: isPrimary
      ? hoveredButton === index
        ? '#3367d6'
        : '#1060E0'
      : hoveredButton === index
      ? '#333'
      : '#161616',

    transform: hoveredButton === index ? 'translateY(-1px)' : 'translateY(0)',
  });

  const featuresStyle = {
    // borderTop: "1px solid #333",
    paddingTop: '12px',
  };

  const featuresTitleStyle = {
    color: '#FCFBF8',
    fontFamily: 'General Sans',
    fontSize: '13.344px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: '21px',
  };

  const featuresListStyle = {
    listStyle: 'none',
    padding: '0',
    margin: '0',
  };

  const featureItemStyle = {
    padding: '8px 0',
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#ccc',
    display: 'flex',
    alignItems: 'flex-start',
  };

  const checkmarkStyle = {
    color: '#4285f4',
    fontWeight: 'bold',
    marginRight: '12px',
    flexShrink: 0,
  };

  return (
    <div style={rootContainer}>
      <div
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 100,
          background: 'rgba(18,18,18,0.1)', // semi-transparent dark
          backdropFilter: 'blur(8px)',
          WebkitBackdropFilter: 'blur(8px)', // for Safari
        }}>
        <Header showLoginModal={showLoginModal} setShowLoginModal={setShowLoginModal} />
      </div>
      <div style={containerStyle}>
        <div style={headerStyle}>
          <div style={logoStyle}>
            <div style={logoIconStyle}>
              <img
                src={require('@/root/web/assets/images/logoSlim.svg')}
                style={{width: 58, height: 48}}
                // resizeMode="contain"
              />
            </div>
          </div>
          <h1 style={titleStyle}>Pricing</h1>
          <p style={subtitleStyle}>
            Start for free. Upgrade to get the capacity that exactly matches your team's needs.
          </p>
          {userLoggedIn && (
            <div
              style={{
                backgroundColor: '#141618',
                width: '60%',
                marginTop: 10,
                alignSelf: 'center',
                borderRadius: '8px',
                alignItems: 'center',
                flexDirection: 'column',
                justifyContent: 'center',
                letterSpacing: '0.4px',
              }}>
              {' '}
              <p
                style={{
                  color: '#C5C1BA',
                  textAlign: 'center' as const,
                  fontFamily: 'General Sans',

                  fontSize: '15px',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  lineHeight: '24px',
                }}>
                You’re currently on the
                <span
                  style={{
                    color: '#1060E0',
                    fontFamily: 'General Sans',
                    fontSize: '15px',
                    fontStyle: 'normal',
                    fontWeight: 600,
                    lineHeight: '24px',
                  }}>
                  {' '}
                  Free
                </span>{' '}
                plan.{' '}
                <span
                onClick={()=> !userLoggedIn && setShowLoginModal(true)}
                  style={{
                    color: '#C5C1BA',
                    fontFamily: 'General Sans',
                    fontSize: '15px',
                    fontStyle: 'normal',
                    fontWeight: 400,
                    lineHeight: '24px',
                    textDecorationLine: 'underline',
                    textDecorationStyle: 'solid',
                    textDecorationSkipInk: 'auto',
                    textDecorationThickness: 'auto',
                    textUnderlineOffset: 'auto',
                    textUnderlinePosition: 'from-font',
                  }}>
                  Upgrade your plan to get credits
                </span>
              </p>
            </div>
          )}
        </div>

        <div style={cardWrapper}>
          <div style={cardsContainerStyle}>
            {/* Free Plan */}
            <div
              style={getCardStyle(0)}
              onMouseEnter={() => setHoveredCard(0)}
              onMouseLeave={() => setHoveredCard(null)}>
              <div style={cardHeaderStyle}>
                <h3 style={planNameStyle}>Free</h3>
                <div style={priceStyle}>
                  <span style={priceAmountStyle}>$0</span>
                  <span style={pricePeriodStyle}>/month</span>
                </div>
                <p style={planDescriptionStyle}>For getting started</p>
              </div>

              <div style={creditsSectionStyle}>
                <div style={creditsDropdownStyle}>1000 monthly credits</div>
              </div>

              <button
                onClick={handleButtonClick}
                style={getButtonStyle(0, true)}
                onMouseEnter={() => setHoveredButton(0)}
                onMouseLeave={() => setHoveredButton(null)}>
                Get started
              </button>

              <div style={featuresStyle}>
                <p style={featuresTitleStyle}>Get started with:</p>
                <ul style={featuresListStyle}>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Unlimited projects</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Visual Editing</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Limited access to Advanced Coding Model</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Chat support via Discord</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Pro Plan */}
            <div
              style={getCardStyle(1, true)}
              onMouseEnter={() => setHoveredCard(1)}
              onMouseLeave={() => setHoveredCard(null)}>
              <div style={popularBadgeStyle}>POPULAR</div>
              <div style={cardHeaderStyle}>
                <h3 style={planNameStyle}>Pro</h3>
                <div style={priceStyle}>
                  <span style={priceAmountStyle}>{creditCostMap[selectedCredits]}</span>
                  <span style={pricePeriodStyle}>/month</span>
                </div>
                <p style={planDescriptionStyle}>For more projects and usage</p>
              </div>

              <div style={creditsSectionStyle}>
                <div style={selectWrapperStyle}>
                  <select value={selectedCredits} onChange={handleSelectChange} style={selectBoxStyle}>
                    <option value="1000">1000 monthly credits</option>
                    <option value="2000">2000 monthly credits</option>
                    <option value="3000">3000 monthly credits</option>
                    <option value="4000">4000 monthly credits</option>
                    <option value="5000">5000 monthly credits</option>
                  </select>
                  <span style={selectArrowStyle}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="7" viewBox="0 0 11 7" fill="none">
                      <path
                        d="M1.31494 1.6228L5.31494 5.6228L9.31494 1.6228"
                        stroke="#D3D3D3"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                      />
                    </svg>
                  </span>
                </div>
              </div>

              <button
                onClick={handleButtonClick}
                style={getButtonStyle(1, true)}
                onMouseEnter={() => setHoveredButton(1)}
                onMouseLeave={() => setHoveredButton(null)}>
                Get Started
              </button>

              <div style={featuresStyle}>
                <p style={featuresTitleStyle}>Everything in Free, plus:</p>
                <ul style={featuresListStyle}>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Generate App build</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>1 Live App on App Store and Play Store</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Push Notifications</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Analytics</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Full Access to Advanced Coding Model</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>15-20x higher rate limits</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Enterprise Plan */}
            <div
              style={getCardStyle(2)}
              onMouseEnter={() => setHoveredCard(2)}
              onMouseLeave={() => setHoveredCard(null)}>
              <div style={cardHeaderStyle}>
                <h3 style={planNameStyle}>Enterprise</h3>
                <div style={priceStyle}>
                  <span style={priceAmountStyle}>Custom</span>
                </div>
                <p style={planDescriptionStyle}>For collaborating with others</p>
              </div>

              <div style={creditsSectionStyle}>
                <div style={creditsInfoStyle}>Unlimited credits</div>
              </div>

              <button
              onClick={()=>{ navigate('../book-a-call')}}
                style={getButtonStyle(2, false)}
                onMouseEnter={() => setHoveredButton(2)}
                onMouseLeave={() => setHoveredButton(null)}>
                📅 Book a Call
              </button>

              <div style={featuresStyle}>
                <p style={featuresTitleStyle}>Everything in Pro, plus:</p>
                <ul style={featuresListStyle}>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Custom Agents</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Human experts to lead your project</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Tile SDK (Code export and Developer tools)</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>Premium Support</span>
                  </li>
                  <li style={featureItemStyle}>
                    <span style={checkmarkStyle}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M4.11492 6.27788L10.1649 0.227881C10.2679 0.127881 10.3881 0.0778809 10.5256 0.0778809C10.6629 0.0778809 10.7816 0.128158 10.8816 0.228714C10.9816 0.329381 11.0316 0.448825 11.0316 0.587047C11.0316 0.725381 10.9816 0.844548 10.8816 0.944548L4.46492 7.36121C4.36492 7.46121 4.24825 7.51121 4.11492 7.51121C3.98158 7.51121 3.86492 7.46121 3.76492 7.36121L0.731583 4.32788C0.631583 4.2271 0.584361 4.10749 0.589916 3.96905C0.595472 3.83049 0.648527 3.71121 0.749083 3.61121C0.84975 3.51121 0.969194 3.46121 1.10742 3.46121C1.24575 3.46121 1.36492 3.51121 1.46492 3.61121L4.11492 6.27788Z"
                          fill="#FCFBF8"
                        />
                      </svg>
                    </span>
                    <span style={textStyle}>No rate limits</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <FAQSection />
      <Footer/>
    </div>
  );
};

export default Pricing;
