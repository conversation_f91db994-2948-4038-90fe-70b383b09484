import {useState} from 'react';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

const FAQSection = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  // Dummy FAQ data
  const faqData: FAQItem[] = [
    {
      id: 1,
      question: 'What is Tile and how does it work?',
      answer:
        "Tile is an AI-powered mobile app builder that lets you create production-grade iOS and Android apps without writing code. You describe what you want, and our AI Agents generate the app's design, logic, and structure. You can then fine-tune the app using Tile’s intuitive no-code editor and publish directly to the App Store and Play Store from a single dashboard.",
    },
    {
      id: 2,
      question: 'What are credits and how do they work?',
      answer:
        'Credits are the currency used inside Tile. Each time you use AI features like generating a new app, adding advanced blocks, or integrating with external services, credits are consumed. You can track your usage in the dashboard and top up or upgrade your plan at any time.',
    },
    {
      id: 3,
      question: 'What tech stacks does Tile use?',
      answer:
        'Tile uses its own proprietary technology built on React Native. This allows Tile to generate cross-platform apps that run smoothly on both iOS and Android. The no-code layer sits on top of this stack, letting users visually edit and configure their apps. You can generate builds and publish directly from a single dashboard.',
    },
    {
      id: 4,
      question: 'Who owns the projects and code?',
      answer:
        'You own your app and everything inside it. The content, logic, and data are yours. While Tile abstracts away the code to simplify the building process, your intellectual property remains fully under your control.',
    },
    {
      id: 5,
      question: 'How can I launch my app with Tile?',
      answer:
        'After building and testing your app on Tile, you can generate production builds and publish directly to the Apple App Store and Google Play Store. Tile guides you through connecting your developer accounts and handles the technical steps of the publishing process.',
    },
    {
      id: 6,
      question: 'What successful apps have been launched using Tile?',
      answer:
        "Pilgrim, a Korean beauty brand, used Tile to launch their e-commerce app. Another example is the Face AI app, which demonstrates the full power of Tile's AI capabilities. It’s ready and will be available on the App Store and Play Store soon.",
    },
  ];

  const handleFAQClick = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  const containerStyle = {
    background: '#000000',
    color: 'white',
    padding: '80px 20px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  };

  const titleStyle = {
    color: '#FCFBF8',
    textAlign: 'center' as const,
    fontFamily: 'General Sans',
    fontSize: '35.297px',
    fontStyle: 'normal' as const,
    fontWeight: 500,
    lineHeight: '45px',
  };

  const faqContainerStyle = {
    maxWidth: '800px',
    margin: '0 auto',
  };

  const faqItemStyle = {
    borderBottom: '1px solid #333',
    marginBottom: '0',
  };

  const questionButtonStyle = {
    width: '100%',
    background: 'transparent',
    border: 'none',
    color: 'white',
    padding: '20px 0',
    fontSize: '20px',
    fontWeight: '400',
    textAlign: 'left' as const,
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    transition: 'color 0.2s ease',
    fontFamily: 'inherit',
  };
  const textStyle = {
    color: '#FCFBF8',
    fontFamily: 'General Sans',
    fontSize: 'clamp(14px , 2vw, 18px)',
    fontStyle: 'normal' as const,
    fontWeight: 500,
    lineHeight: '30px',
  };

  const questionButtonHoverStyle = {
    color: '#ccc',
  };

  const chevronStyle = (isOpen: boolean) => ({
    fontSize: '16px',
    transition: 'transform 0.3s ease',
    transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
    color: '#888',
  });

  const answerContainerStyle = (isOpen: boolean) => ({
    maxHeight: isOpen ? '500px' : '0',
    overflow: 'hidden',
    transition: 'max-height 0.3s ease, padding 0.3s ease',
    padding: isOpen ? '0 0 32px 0' : '0',
  });

  const answerStyle = {
    fontSize: 'clamp(12px, 2vw, 16px)',
    lineHeight: '1.6',
    color: '#ccc',
    paddingRight: '40px',
  };

  return (
    <div style={containerStyle}>
      <h2 style={titleStyle}>
        Frequently Asked
        <br />
        Questions
      </h2>

      <div style={faqContainerStyle}>
        {faqData.map(faq => (
          <div key={faq.id} style={faqItemStyle}>
            <button
              style={questionButtonStyle}
              onClick={() => handleFAQClick(faq.id)}
              onMouseEnter={e => {
                e.currentTarget.style.color = questionButtonHoverStyle.color;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.color = 'white';
              }}>
              <span style={textStyle}>{faq.question}</span>
              <span style={chevronStyle(openFAQ === faq.id)}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M3.3442 6.56859C3.54566 6.3537 3.88317 6.34282 4.09805 6.54427L7.99995 10.2023L11.9018 6.54427C12.1168 6.34282 12.4542 6.3537 12.6557 6.56859C12.8571 6.78348 12.8462 7.121 12.6314 7.32245L8.36472 11.3225C8.15957 11.5148 7.84034 11.5148 7.63518 11.3225L3.36852 7.32245C3.15363 7.121 3.14275 6.78348 3.3442 6.56859Z"
                    fill="#C5C1BA"
                  />
                </svg>
              </span>
            </button>

            <div style={answerContainerStyle(openFAQ === faq.id)}>
              <div style={answerStyle}>{faq.answer}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FAQSection;
