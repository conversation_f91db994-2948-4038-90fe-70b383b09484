'use client';

import {useState} from 'react';
import {useNavigate} from '@/root/web/routing.web';
import {useDispatch} from 'react-redux';
import {makeToast} from '@/root/web/actions/toastActions';
import {borderColor} from '../editor/components/svgelems';

interface FormData {
  firstName: string;
  lastName: string;
  workEmail: string;
  phoneNumber: string;
  companyWebsite: string;
  // Step 2 fields (you can add more as needed)
  timeline: string;
  appRequirement: string;
}

const BookACall = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    workEmail: '',
    phoneNumber: '',
    companyWebsite: '',
    timeline: '',
    appRequirement: '',
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    let error = '';
    if (field === 'workEmail' && value && !isValidEmail(value)) {
      error = 'Invalid email address';
    } else if (field === 'phoneNumber' && value && !isValidPhoneNumber(value)) {
      error = 'Invalid phone number';
    } else if (field === 'companyWebsite' && value && !isValidWebsite(value)) {
      error = 'Invalid website URL';
    }

    setErrors(prev => ({
      ...prev,
      [field]: error,
    }));
  };

  const handleSubmit = async () => {
    const urlEncodedBody = new URLSearchParams({
      firstName: formData.firstName,
      lastName: formData.lastName,
      workEmail: formData.workEmail,
      phoneNumber: formData.phoneNumber,
      companyWebsite: formData.companyWebsite,
      timeline: formData.timeline,
      appRequirement: formData.appRequirement,
    }).toString();
    const url =
      'https://script.google.com/macros/s/AKfycbxfpsnv-itdgrltj1ddqMlHXZXnm8DGZ4RL4jpoaPJCUj0JPtbRGrbuqNk8_JZ-jZIPwQ/exec';
    const response = await fetch(url, {
      method: 'POST',
      body: urlEncodedBody,

      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    setFormData({
      firstName: '',
      lastName: '',
      workEmail: '',
      phoneNumber: '',
      companyWebsite: '',
      timeline: '',
      appRequirement: '',
    });
    if (response.ok) {
      dispatch(makeToast({content: 'Submitted Successfully', appearances: 'success'}));
    } else {
      dispatch(makeToast({content: 'Error while submitting Details', appearances: 'error'}));
    }
  };

  const handleContinue = () => {
    if (currentStep === 1) {
      setCurrentStep(2);
    } else {
      // Final submission
      console.log('Form Data:', formData);
      handleSubmit();
      //   alert('Form submitted! Check console for data.');
      window.open(
        'https://meetings.hubspot.com/devdutt-manay/demo-tiledev-enterprise?uuid=7346b371-15b2-4c20-84bc-8514dd70baa1',
        '_blank',
      );
    }
  };
  const handlePrevious = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const isStep1Valid = () => {
    return (
      formData.firstName.trim() &&
      formData.lastName.trim() &&
      isValidEmail(formData.workEmail) &&
      isValidWebsite(formData.companyWebsite)
    );
  };

  const isStep2Valid = () => {
    return formData.timeline && formData.appRequirement;
  };
  const isValidEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email.toLowerCase());
  };

  const isValidPhoneNumber = (phone: string) => {
    const re = /^\+?[0-9]{10,15}$/; // Accepts digits only, length 10–15
    return re.test(phone);
  };

  const isValidWebsite = (url: string) => {
    const re = /^(https?:\/\/)?(www\.)?[\w-]+\.\w{2,}(\/\S*)?$/i;
    return re.test(url);
  };

  const rootContainer = {
    backgroundColor: '#000000',
    color: 'white',
    margin: 0,
    // minHeight: "100vh",
    height: '100vh',
    overflowX: 'scroll',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    scrollbarWidth: 'none',
  };
  // Styles
  const containerStyle = {
    display: 'flex',
    minHeight: '100vh',
    background: '#1A1D20',
    color: 'white',
    // overflowX: 'scroll',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  };

  const leftSideStyle = {
    flex: '1',
    padding: '0 40px',
    display: 'flex',
    flexDirection: 'column' as const,
    // maxWidth: "500px",
  };

  const logoStyle = {
    width: '50px',
    height: '50px',
    borderRadius: '12px',
    marginBottom: '40px',
    position: 'relative' as const,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  const logoIconStyle = {
    width: '0',
    height: '0',
    borderLeft: '8px solid white',
    borderTop: '6px solid transparent',
    borderBottom: '6px solid transparent',
    marginLeft: '2px',
  };

  const progressContainerStyle = {
    marginBottom: '40px',
  };

  const progressBarStyle = {
    width: '200px',
    height: '4px',
    background: '#333',
    borderRadius: '2px',
    marginBottom: '12px',
    overflow: 'hidden',
  };

  const progressFillStyle = {
    height: '100%',
    background: '#4285f4',
    borderRadius: '2px',
    width: currentStep === 1 ? '50%' : '100%',
    transition: 'width 0.3s ease',
  };

  const stepTextStyle = {
    fontSize: '14px',
    color: '#fff',
    fontWeight: '500',
  };

  const titleStyle = {
    color: '#FFF',
    fontFamily: 'General Sans',
    fontSize: '43px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '120%',
  };

  const subtitleStyle = {
    color: '#FFF',
    fontFamily: 'General Sans',
    fontSize: '18px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '120%',
  };

  const rightSideStyle = {
    flex: '2',
    padding: '120px 240px',
    // maxWidth: "600px",
    backgroundColor: '#141618',
  };

  const sectionTitleStyle = {
    color: '#FFF',
    fontFamily: 'General Sans',
    fontSize: '34px',
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: '120%',
  };

  const formGroupStyle = {
    marginBottom: '24px',
  };

  const labelStyle = {
    fontFamily: 'General Sans',
    fontSize: '14px',
    fontWeight: 500,
    color: '#fff',
    display: 'block', // This is important
    marginBottom: '8px',
    lineHeight: '120%',
  };

  const inputStyle = {
    width: '100%',
    padding: '16px',
    background: '#1A1D20',
    border: 'none',
    borderRadius: '8px',
    color: 'white',
    fontSize: '16px',
    fontFamily: 'inherit',
    transition: 'border-color 0.2s ease',
  };

  const inputFocusStyle = {};

  const selectStyle = {
    ...inputStyle,
    cursor: 'pointer',
  };

  const textareaStyle = {
    ...inputStyle,
    minHeight: '120px',
    resize: 'vertical' as const,
  };

  const twoColumnStyle = {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: '40px',
  };

  const requiredTextStyle = {
    fontSize: '14px',
    color: '#888',
    // marginBottom: "32px",
    //    float:'right'
  };

  const buttonStyle = {
    background: '#003773',
    color: '#FAFAFA',
    border: 'none',
    padding: '12px 24px',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '500',
    cursor: 'pointer',
    marginTop: 20,
    transition: 'background 0.2s ease',
  };
  const backButton = {
    background: '#1A1D20',
    borderColor: 'transparent',
    border: 'none',
    color: '#FAFAFA',
    fontSize: '20px',
    fontWeight: '500',
    cursor: 'pointer',
  };

  const buttonHoverStyle = {
    background: '#3367d6',
  };

  const buttonDisabledStyle = {
    background: '#555',
    cursor: 'not-allowed',
  };

  return (
    <div style={rootContainer}>
      <div style={containerStyle}>
        {/* Left Side - Static */}

        <div style={leftSideStyle}>
          <div>
            <button
              style={backButton}
              onClick={() => {
                navigate(-1);
              }}>
              <p>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="14" viewBox="0 0 18 14" fill="none">
                  <path
                    d="M17 6.99023H1M1 6.99023L7 0.990234M1 6.99023L7 12.9902"
                    stroke="#EFEFEF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </p>
            </button>
          </div>
          <div style={{padding: '60px 0px'}}>
            <div style={logoStyle}>
              <img
                src={require('@/root/web/assets/images/logoSlim.svg')}
                style={{width: 58, height: 48}}
                // resizeMode="contain"
              />
            </div>

            <div style={progressContainerStyle}>
              <div style={progressBarStyle}>
                <div style={progressFillStyle}></div>
              </div>
              <div style={stepTextStyle}>{currentStep} of 2</div>
            </div>

            <h1 style={titleStyle}>Tell us more about yourself</h1>
            <p style={subtitleStyle}>This helps us get to know you and your requirements</p>
          </div>
        </div>

        {/* Right Side - Dynamic Content */}
        <div style={rightSideStyle}>
          {currentStep === 1 && (
            <>
              <h2 style={sectionTitleStyle}>Additional Information</h2>

              <div style={twoColumnStyle}>
                <div style={formGroupStyle}>
                  <label style={labelStyle}>First Name*</label>
                  <input
                    type="text"
                    placeholder="John"
                    value={formData.firstName}
                    onChange={e => handleInputChange('firstName', e.target.value)}
                    style={inputStyle}
                    onFocus={e => (e.target.style.borderColor = inputFocusStyle.borderColor)}
                    onBlur={e => (e.target.style.borderColor = '#444')}
                  />
                </div>

                <div style={formGroupStyle}>
                  <label style={labelStyle}>Last Name*</label>
                  <input
                    type="text"
                    placeholder="Doe"
                    value={formData.lastName}
                    onChange={e => handleInputChange('lastName', e.target.value)}
                    style={inputStyle}
                    onFocus={e => (e.target.style.borderColor = inputFocusStyle.borderColor)}
                    onBlur={e => (e.target.style.borderColor = '#444')}
                  />
                </div>
              </div>

              <div style={formGroupStyle}>
                <label style={labelStyle}>Work email*</label>
                <input
                  type="email"
                  placeholder="e.g. <EMAIL>"
                  value={formData.workEmail}
                  onChange={e => handleInputChange('workEmail', e.target.value)}
                  style={inputStyle}
                  onFocus={e => (e.target.style.borderColor = inputFocusStyle.borderColor)}
                  onBlur={e => (e.target.style.borderColor = '#444')}
                />
                {errors.workEmail && (
                  <div style={{color: 'red', marginTop: 4, fontSize: '12px'}}>{errors.workEmail}</div>
                )}
              </div>

              <div style={formGroupStyle}>
                <label style={labelStyle}>Phone number</label>
                <input
                  type="tel"
                  placeholder="e.g. +14155552671"
                  value={formData.phoneNumber}
                  onChange={e => {
                    const value = e.target.value;
                    if (/^\+?[0-9]*$/.test(value)) {
                      handleInputChange('phoneNumber', value);
                    }
                  }}
                  style={inputStyle}
                  onFocus={e => (e.target.style.borderColor = inputFocusStyle.borderColor)}
                  onBlur={e => (e.target.style.borderColor = '#444')}
                />
                {errors.phoneNumber && (
                  <div style={{color: 'red', marginTop: 4, fontSize: '12px'}}>{errors.phoneNumber}</div>
                )}
              </div>

              <div style={formGroupStyle}>
                <label style={labelStyle}>Company's website*</label>
                <input
                  type="url"
                  placeholder="e.g. company.com"
                  value={formData.companyWebsite}
                  onChange={e => handleInputChange('companyWebsite', e.target.value)}
                  style={inputStyle}
                  onFocus={e => (e.target.style.borderColor = inputFocusStyle.borderColor)}
                  onBlur={e => (e.target.style.borderColor = '#444')}
                />
                {errors.companyWebsite && (
                  <div style={{color: 'red', marginTop: 4, fontSize: '12px'}}>{errors.companyWebsite}</div>
                )}
              </div>

              <div style={{width: '100%', display: 'flex', alignItems: 'flex-end', flexDirection: 'column'}}>
                <div style={requiredTextStyle}>*Required fields</div>

                <button
                  style={{
                    ...buttonStyle,
                    ...(isStep1Valid() ? {} : buttonDisabledStyle),
                  }}
                  onClick={handleContinue}
                  disabled={!isStep1Valid()}
                  onMouseEnter={e => {
                    if (isStep1Valid()) {
                      e.currentTarget.style.background = buttonHoverStyle.background;
                    }
                  }}
                  onMouseLeave={e => {
                    if (isStep1Valid()) {
                      e.currentTarget.style.background = buttonStyle.background;
                    }
                  }}>
                  Continue
                </button>
              </div>
            </>
          )}

          {currentStep === 2 && (
            <>
              <h2 style={sectionTitleStyle}>Additional Information</h2>

              <div style={formGroupStyle}>
                <label style={labelStyle}>App requirement</label>
                <input
                  type="text"
                  placeholder="e.g. We are a company that runs a business and want to use your platform for scaling our design processes to reduce time consumption..."
                  value={formData.appRequirement}
                  onChange={e => handleInputChange('appRequirement', e.target.value)}
                  style={{...inputStyle, ...{height: 50}}}
                  onFocus={e => (e.target.style.borderColor = inputFocusStyle.borderColor)}
                  onBlur={e => (e.target.style.borderColor = '#444')}
                />
              </div>
              <div style={formGroupStyle}>
                <label style={labelStyle}>Do you have a timeline to getting started?</label>
                <select
                  value={formData.timeline}
                  onChange={e => handleInputChange('timeline', e.target.value)}
                  style={selectStyle}
                  onFocus={e => (e.target.style.borderColor = inputFocusStyle.borderColor)}
                  onBlur={e => (e.target.style.borderColor = '#444')}>
                  <option value="">Select your timeline</option>
                  <option value="as soon as possible">As soon as possible</option>
                  <option value="coming week">Coming week</option>
                  <option value="next month">Next month</option>
                  <option value="more than 2months">More than 2 months</option>
                </select>
              </div>

              <div style={{width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <div>
                  <button
                    style={{
                      ...buttonStyle,
                      ...{backgroundColor: '#1A1D20'},
                    }}
                    onClick={handlePrevious}>
                    Previous
                  </button>
                </div>
                <div>
                  {' '}
                  <div style={requiredTextStyle}>*Required fields</div>
                  <button
                    style={{
                      ...buttonStyle,
                      ...(isStep2Valid() ? {} : buttonDisabledStyle),
                    }}
                    onClick={handleContinue}
                    disabled={!isStep2Valid()}
                    onMouseEnter={e => {
                      if (isStep2Valid()) {
                        e.currentTarget.style.background = buttonHoverStyle.background;
                      }
                    }}
                    onMouseLeave={e => {
                      if (isStep2Valid()) {
                        e.currentTarget.style.background = buttonStyle.background;
                      }
                    }}>
                    Submit
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

// function FormToSheet() {

//     const handleSubmit = (e)=>{
//       e.preventDefault()
//        const url = "https://script.google.com/macros/s/AKfycbxfpsnv-itdgrltj1ddqMlHXZXnm8DGZ4RL4jpoaPJCUj0JPtbRGrbuqNk8_JZ-jZIPwQ/exec"
//       fetch(url,{
//         method:"POST",
//         headers: { "Content-Type": "application/x-www-form-urlencoded" },
//         body:(`Name=${e.target.name.value}&Email=${e.target.email.value}`)
//       }).then(res=>res.text()).then(data=>{
//         alert(data)
//         console.log(data);
//       }).catch(error=>console.log(error))
//     }

//     return (
//       <div>
//           <h1>React to Sheet</h1>
//           <form onSubmit={handleSubmit}>
//             <input name='name' placeholder='Name 1' /> <br/>
//             <input name='email' placeholder='Email 1' /> <br/>
//             <button>Add</button>
//           </form>
//       </div>
//     )
//   }

export default BookACall;
