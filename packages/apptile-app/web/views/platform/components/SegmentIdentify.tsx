import {myAddOns, selectCurrentPlanWithDetails, selectCurrentSubscription} from '@/root/web/selectors/BillingSelector';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import React, {useEffect} from 'react';
import Analytics from '@/root/web/lib/segment';
import {useSelector} from 'react-redux';
import {compileContactInfoData} from './compileStoreLeadsContactInfo';
import {APP_PREVIEW_CLICKED} from '@/root/web/common/onboardingConstants';
import {getOnboardingMetadataWithKey} from '@/root/web/selectors/OnboardingSelector';

const getBrandSelector = (state: EditorRootState) => {
  return state.appModel?.getModelValue(['shopify', 'shop']);
};

const getApptileBrandSelector = (state: EditorRootState) => {
  return state.onboarding.brandData;
};

export const SegmentIdentify = ({orgId}: {orgId: string}) => {
  const {name: planName} = useSelector(selectCurrentPlanWithDetails) ?? {};
  const currentActiveSubscription = useSelector(selectCurrentSubscription);
  const {userLoggedIn, userFetched, user} = useSelector((state: EditorRootState) => state.user);
  const {orgsById} = useSelector((state: EditorRootState) => state.orgs);
  const shopDomain = useSelector(getBrandSelector)?.primaryDomain?.host;

  const apptileBrand = useSelector(getApptileBrandSelector);
  const {sales, storeLeads, fetching: apptileBrandFetching} = apptileBrand;
  const {convertedUSDAmount, amount} = sales ?? {};
  const isPublished = useSelector((state: EditorRootState) => state.apptile.isPublished);
  const previewClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, APP_PREVIEW_CLICKED),
  );

  const {
    estimated_visits,
    created_at,
    apps: shopifyApps,
    contact_info,
    plan: shopifyPlan,
    description,
    title,
    about_us,
    country_code,
    technologies,
  } = storeLeads ?? {};
  const {
    instagramFollowers,
    instagramHandle,
    facebookFollowers,
    facebookHandle,
    linkedinFollowers,
    twitterFollowers,
    twitterHandle,
    phoneNumber,
    linkedinHandle,
    youtubeFollowers,
    youtubeHandle,
  } = compileContactInfoData(contact_info);
  const purchasedAddons = useSelector(myAddOns);

  useEffect(() => {
    if (user && userFetched && userLoggedIn && orgId) {
      console.log('User identified: ', shopDomain);
      Analytics.identify(user.id as any, {
        name: `${user.firstname} ${user.lastname}`,
        email: user.email,
        plan: currentActiveSubscription ? planName : null,
        shopDomain: shopDomain ?? null,
        monthlySales: JSON.stringify(sales, null, 4),
        monthlySalesAmount: amount,
        monthlySalesUSDAmount: convertedUSDAmount,
        monthlyWebsiteTraffic: estimated_visits,
        shopCreatedDate: `On or before ${created_at && new Date(created_at)?.toISOString()?.split('T')[0]}`,
        shopifyApps: JSON.stringify(shopifyApps, null, 4),
        instagramHandle: instagramHandle,
        instagramFollowers: instagramFollowers,
        youtubeHandle: youtubeHandle,
        youtubeFollowers: youtubeFollowers,
        facebookHandle: facebookHandle,
        facebookFollowers: facebookFollowers,
        linkedinHandle: linkedinHandle,
        linkedinFollowers: linkedinFollowers,
        twitterHandle: twitterHandle,
        twitterFollowers: twitterFollowers,
        contactNumber: phoneNumber,
        shopifyPlan: shopifyPlan,
        shopDescription: description,
        shopName: title,
        aboutUsPage: about_us,
        countryCode: country_code,
        technologiesUsed: JSON.stringify(technologies, null, 4),
        appLiveWithApptile: isPublished,
        purchasedAddOns: JSON.stringify(purchasedAddons, null, 4),
        first_app_preview: !!previewClicked,
      });
    }
  }, [
    user,
    userLoggedIn,
    userFetched,
    orgId,
    planName,
    currentActiveSubscription,
    shopDomain,
    apptileBrandFetching,
    purchasedAddons,
    previewClicked,
    convertedUSDAmount,
  ]);

  return <></>;
};
