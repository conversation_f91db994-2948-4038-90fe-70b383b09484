import React, {useEffect, useState} from 'react';
import Header from './header';
import Stepper from './stepper';
import AppDetails from './appDetails';
import FirebaseDetails from './firebasedetails';
import {usePublishContext} from '../context';
import AppstoreDetails from './appstoreDetails';
import PlaystoreDetailsPage from './playstoreDetailsPage';
import BuildList from './buildsList';
import Footer from './footer';
import {darkTheme} from '../styles';
import LeftSidebar from '../../prompt-to-app/dashboard/LeftSidebar';
import {NavigationProvider} from '@/root/web/contexts/NavigationContext';
import Analytics from '@/root/web/lib/segment';

const PublishFlow: React.FC = () => {
  const {step, appDetails, firebaseProjectId, buildDetails} = usePublishContext();

  // State for AppDetails footer
  const [appDetailsHandleSubmit, setAppDetailsHandleSubmit] = useState<(() => Promise<void>) | null>(null);
  const [appDetailsCanProceed, setAppDetailsCanProceed] = useState(false);

  // State for FirebaseDetails footer
  const [firebaseDetailsHandleSubmit, setFirebaseDetailsHandleSubmit] = useState<(() => Promise<void>) | null>(null);
  const [firebaseDetailsCanProceed, setFirebaseDetailsCanProceed] = useState(false);

  // State for AppstoreDetails footer
  const [appstoreDetailsHandleSubmit, setAppstoreDetailsHandleSubmit] = useState<(() => Promise<void>) | null>(null);
  const [appstoreDetailsCanProceed, setAppstoreDetailsCanProceed] = useState(false);

  // State for PlaystoreDetailsPage footer
  const [playstoreDetailsHandleSubmit, setPlaystoreDetailsHandleSubmit] = useState<(() => Promise<void>) | null>(null);
  const [playstoreDetailsCanProceed, setPlaystoreDetailsCanProceed] = useState(false);

  // State for BuildList footer
  const [buildListHandleSubmit, setBuildListHandleSubmit] = useState<(() => Promise<void>) | null>(null);
  const [buildListCanProceed, setBuildListCanProceed] = useState(false);

  // Debug logs
  // console.log('PublishFlow: appDetailsHandleSubmit', !!appDetailsHandleSubmit);
  // console.log('PublishFlow: appDetailsCanProceed', appDetailsCanProceed);
  // console.log('PublishFlow: firebaseDetailsHandleSubmit', !!firebaseDetailsHandleSubmit);
  // console.log('PublishFlow: firebaseDetailsCanProceed', firebaseDetailsCanProceed);
  // console.log('PublishFlow: appstoreDetailsHandleSubmit', !!appstoreDetailsHandleSubmit);
  // console.log('PublishFlow: appstoreDetailsCanProceed', appstoreDetailsCanProceed);
  // console.log('PublishFlow: playstoreDetailsHandleSubmit', !!playstoreDetailsHandleSubmit);
  // console.log('PublishFlow: playstoreDetailsCanProceed', playstoreDetailsCanProceed);
  // console.log('PublishFlow: buildListHandleSubmit', !!buildListHandleSubmit);
  // console.log('PublishFlow: buildListCanProceed', buildListCanProceed);

  // Callback functions with debug logs for AppDetails
  const handleAppDetailsSubmit = (submitFn: () => Promise<void>) => {
    console.log('PublishFlow: Received handleSubmit from AppDetails');
    setAppDetailsHandleSubmit(() => submitFn);
  };

  const handleAppDetailsCanProceedChange = (canProceed: boolean) => {
    console.log('PublishFlow: Received canProceed from AppDetails:', canProceed);
    setAppDetailsCanProceed(canProceed);
  };

  // Callback functions with debug logs for FirebaseDetails
  const handleFirebaseDetailsSubmit = (submitFn: () => Promise<void>) => {
    console.log('PublishFlow: Received handleSubmit from FirebaseDetails');
    setFirebaseDetailsHandleSubmit(() => submitFn);
  };

  const handleFirebaseDetailsCanProceedChange = (canProceed: boolean) => {
    console.log('PublishFlow: Received canProceed from FirebaseDetails:', canProceed);
    setFirebaseDetailsCanProceed(canProceed);
  };

  // Callback functions with debug logs for AppstoreDetails
  const handleAppstoreDetailsSubmit = (submitFn: () => Promise<void>) => {
    console.log('PublishFlow: Received handleSubmit from AppstoreDetails');
    setAppstoreDetailsHandleSubmit(() => submitFn);
  };

  const handleAppstoreDetailsCanProceedChange = (canProceed: boolean) => {
    console.log('PublishFlow: Received canProceed from AppstoreDetails:', canProceed);
    setAppstoreDetailsCanProceed(canProceed);
  };

  // Callback functions with debug logs for PlaystoreDetailsPage
  const handlePlaystoreDetailsSubmit = (submitFn: () => Promise<void>) => {
    console.log('PublishFlow: Received handleSubmit from PlaystoreDetailsPage');
    setPlaystoreDetailsHandleSubmit(() => submitFn);
  };

  const handlePlaystoreDetailsCanProceedChange = (canProceed: boolean) => {
    console.log('PublishFlow: Received canProceed from PlaystoreDetailsPage:', canProceed);
    setPlaystoreDetailsCanProceed(canProceed);
  };

  // Callback functions with debug logs for BuildList
  const handleBuildListSubmit = (submitFn: () => Promise<void>) => {
    console.log('PublishFlow: Received handleSubmit from BuildList');
    setBuildListHandleSubmit(() => submitFn);
  };

  const handleBuildListCanProceedChange = (canProceed: boolean) => {
    console.log('PublishFlow: Received canProceed from BuildList:', canProceed);
    setBuildListCanProceed(canProceed);
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return <AppDetails onSubmit={handleAppDetailsSubmit} onCanProceedChange={handleAppDetailsCanProceedChange} />;
      case 2:
        return (
          <FirebaseDetails
            onSubmit={handleFirebaseDetailsSubmit}
            onCanProceedChange={handleFirebaseDetailsCanProceedChange}
          />
        );
      case 3:
        return (
          <AppstoreDetails
            onSubmit={handleAppstoreDetailsSubmit}
            onCanProceedChange={handleAppstoreDetailsCanProceedChange}
          />
        );
      case 4:
        return (
          <PlaystoreDetailsPage
            onSubmit={handlePlaystoreDetailsSubmit}
            onCanProceedChange={handlePlaystoreDetailsCanProceedChange}
          />
        );
      case 5:
        return <BuildList onSubmit={handleBuildListSubmit} onCanProceedChange={handleBuildListCanProceedChange} />;
      default:
        return null;
    }
  };

  // Render footer for all steps
  const renderFooter = () => {
    switch (step) {
      case 1:
        return (
          <Footer canProceed={appDetailsCanProceed} onNext={appDetailsHandleSubmit || (() => Promise.resolve())} />
        );
      case 2:
        return (
          <Footer
            canProceed={firebaseDetailsCanProceed}
            onNext={firebaseDetailsHandleSubmit || (() => Promise.resolve())}
          />
        );
      case 3:
        return (
          <Footer
            canProceed={appstoreDetailsCanProceed}
            onNext={appstoreDetailsHandleSubmit || (() => Promise.resolve())}
          />
        );
      case 4:
        return (
          <Footer
            canProceed={playstoreDetailsCanProceed}
            onNext={playstoreDetailsHandleSubmit || (() => Promise.resolve())}
          />
        );
      case 5:
        return <Footer canProceed={buildListCanProceed} onNext={buildListHandleSubmit || (() => Promise.resolve())} />;
      default:
        return null;
    }
  };

  useEffect(() => {
    Analytics.pageView('build_page_viewed');
  }, []);

  return (
    <NavigationProvider>
      <div style={styles.container}>
        <div style={styles.mainContainer}>
          <LeftSidebar mainBar="APP_EDITOR" />
          <div style={styles.rightContainer}>
            <Header />
            <div style={styles.wrapper}>
              <Stepper />
              <div style={styles.content}>{renderStep()}</div>
            </div>
            {renderFooter()}
          </div>
        </div>
      </div>
    </NavigationProvider>
  );
};

const styles = {
  container: {
    display: 'flex' as const,
    flexDirection: 'column' as const,
    height: '100vh',
    overflow: 'hidden' as const,
    backgroundColor: darkTheme.background,
    // border: '2px solid red',
  },
  mainContainer: {
    display: 'flex' as const,
    flexDirection: 'row' as const,
    height: '100%',
    maxHeight: '100vh',
    flex: 1,
    overflow: 'hidden' as const,
    // border: '2px solid green',
  },
  rightContainer: {
    display: 'flex' as const,
    flexDirection: 'column' as const,
    flex: 1,
    borderLeftWidth: 1,
    borderLeftColor: '#333333',
    borderLeftStyle: 'solid' as const,
    height: '100%',
    overflow: 'hidden' as const,
    // border: '2px solid yellow',
  },
  wrapper: {
    flex: 1,
    padding: 10,
    backgroundColor: darkTheme.background,
    display: 'flex' as const,
    flexDirection: 'column' as const,
    overflow: 'hidden' as const,
    minHeight: 0, // This forces the wrapper to respect parent height
    // border: '2px solid pink',
  },
  content: {
    flex: 1,
    // overflow: 'scroll',
    minHeight: 0, // Allow content to shrink
    // border: '2px solid red',
  },
};

export default PublishFlow;
