import React, {useEffect, useState} from 'react';
import NavigationStepper from './components/NavigationStepper';
import {Pressable, StyleSheet, View} from 'react-native';
import Button from '../../components-v2/base/Button';
import AppDetails from './components/AppDetails';
import AccountDetails from './components/AccountDetails';
import Publishing from './components/Publishing';
import {BuildManagerApi} from '../../api/BuildApi';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import TextElement from '../../components-v2/base/TextElement';
import {MaterialCommunityIcons} from 'apptile-core';
import Analytics from '@/root/web/lib/segment';
import _ from 'lodash';
import ModalComponent from '../../components-v2/base/Modal';
import {makeToast} from '../../actions/toastActions';

export interface IFormData {
  appName: string;
  appSubtitle: string;
  privacyPolicy: string;
  appDescription: string;
  hasDeveloperAccount: boolean;
}
interface IPublishFlow {
  onCancel: any;
}

const PublishFlow = ({onCancel}: IPublishFlow) => {
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState(1);
  const [getRequestLoading, setGetRequestLoading] = useState(true);
  const [hasDeveloperAccount, setHasDeveloperAccount] = useState('Yes');
  const [openCancelModal, setOpenCancelModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navLength = 3;

  const [formData, setFormData] = useState<IFormData>({
    appName: '',
    appSubtitle: '',
    privacyPolicy: '',
    appDescription: '',
    hasDeveloperAccount: false,
  });

  const onFormChange = (data: string | boolean, field: string) => {
    setFormData((oldState: IFormData) => {
      return {
        ...oldState,
        [field]: data,
      };
    });
  };

  useEffect(() => {
    if (currentStep === 3 && appId && !_.isEmpty(formData.appName)) {
      setIsSubmitting(true);
      BuildManagerApi.postAppMetadata(appId as string, formData)
        .then(() => {
          dispatch(makeToast({content: 'Publish successful!', appearances: 'success', cancellable: true}));
          Analytics.track('editor:publish_publishSuccessful');
          setIsSubmitting(false);
        })
        .catch((error: any) => {
          logger.error('Publish error: ', error.message);
          dispatch(makeToast({content: 'Publish failed please try again!', appearances: 'error', cancellable: true}));
          setCurrentStep(1);
          setIsSubmitting(false);
        });
    }
  }, [currentStep, appId, formData, dispatch]);

  useEffect(() => {
    BuildManagerApi.getAppMetadata(appId as string)
      .then(response => {
        setHasDeveloperAccount(response?.data?.hasDeveloperAccount ? 'Yes' : 'No');
        setCurrentStep(3);
        setGetRequestLoading(false);
      })
      .catch(() => {
        setGetRequestLoading(false);
      });
  }, []);

  const onCancelPress = () => {
    if (currentStep === 3) {
      onCancel();
    } else {
      setOpenCancelModal(true);
    }
  };

  return (
    <View style={styles.cancelWrapper}>
      <Pressable style={styles.cancelButton} onPress={onCancelPress}>
        <MaterialCommunityIcons name={'close'} color={'#000000'} size={20} />
      </Pressable>
      {!getRequestLoading && !isSubmitting ? (
        <>
          <NavigationStepper location={currentStep} />
          {currentStep === 1 && <AppDetails formData={formData} onFormChange={onFormChange} />}
          {currentStep === 2 && (
            <AccountDetails
              formData={formData}
              onFormChange={onFormChange}
              onSelection={(value: string) => setHasDeveloperAccount(value)}
            />
          )}
          {currentStep === 3 && <Publishing hasDeveloperAccount={hasDeveloperAccount} />}

          <View style={styles.buttonWrapper}>
            {currentStep === 2 && (
              <Button onPress={() => setCurrentStep(currentStep - 1)} containerStyles={{width: 115}} color="SECONDARY">
                Previous
              </Button>
            )}
            <View />
            {currentStep !== navLength && (
              <Button
                onPress={() => {
                  if (currentStep === 1) {
                    Analytics.track('app_details_saved');
                  } else if (currentStep === 2) {
                    Analytics.track('firebase_details_saved');
                  } else if (currentStep === 3) {
                    Analytics.track('app_store_details_saved');
                  } else if (currentStep === 4) {
                    Analytics.track('play_store_details_saved');
                  }
                  setCurrentStep(currentStep + 1);
                }}
                disabled={
                  _.isEmpty(formData.appName) ||
                  _.isEmpty(formData.privacyPolicy) ||
                  _.isEmpty(formData.appDescription) ||
                  _.isEmpty(formData.appSubtitle)
                }
                containerStyles={{width: 115}}
                color="CTA">
                {currentStep !== 2 ? <>Next</> : <>Submit</>}
              </Button>
            )}
          </View>
        </>
      ) : (
        <TextElement color="SECONDARY">Loading...</TextElement>
      )}
      <View style={{position: 'absolute'}}>
        <ModalComponent
          disableOutsideClick
          visible={openCancelModal}
          content={<ConfirmationModalContent setOpenCancelModal={setOpenCancelModal} onCancel={onCancel} />}
        />
      </View>
    </View>
  );
};

const ConfirmationModalContent = ({setOpenCancelModal, onCancel}: {setOpenCancelModal: any; onCancel: any}) => {
  return (
    <View style={styles.cancelContentWrapper}>
      <TextElement fontSize="md" color="SECONDARY">
        Are you sure? All un-submitted data will be lost.
      </TextElement>
      <View style={[styles.flexRow, styles.marginTop20]}>
        <Button onPress={() => setOpenCancelModal(false)} color="CTA">
          Back
        </Button>
        <Button onPress={onCancel} color="ERROR">
          Sure
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  buttonWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'absolute',
    bottom: 10,
    right: 0,
    left: 0,
  },
  cancelButton: {
    position: 'absolute',
    top: -20,
    right: -10,
  },
  cancelWrapper: {width: 607, height: 435, position: 'relative'},
  flexRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cancelContentWrapper: {
    paddingHorizontal: 30,
    paddingVertical: 20,
  },
  marginTop20: {
    marginTop: 20,
  },
});

export default PublishFlow;
