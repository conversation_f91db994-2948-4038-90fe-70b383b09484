import React from 'react';
import {useEffect, useState} from 'react';
import {StyleSheet, TextInput, Text, TouchableOpacity, View} from 'react-native';
import {useParams} from 'react-router-dom';
import IntegrationsApi from '../../../api/IntegrationsApi';
import TextElement from '../../../components-v2/base/TextElement';
import theme from '../../../views/prompt-to-app/styles-prompt-to-app/theme';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import Icon from 'react-native-vector-icons/AntDesign';
import {useDispatch} from 'react-redux';
import {makeToast} from '../../../actions/toastActions';
import {modelUpdateAction, store} from 'apptile-core';
import {ScrollView} from 'react-native-gesture-handler';
import {ActivityIndicator} from 'react-native';
import Analytics from '@/root/web/lib/segment';

const PLATFORM_TYPE = 'inAppPurchases';

const webStyles = {
  logoImage: {width: 50, height: 50, objectFit: 'contain'},
  bannerImage: {height: '100%', objectFit: 'fit', aspectRatio: '530/621'},
};

const addButtonStyle = {
  borderRadius: 8,
  backgroundColor: '#005be4',
  marginLeft: 0,
  height: 30,
  paddingHorizontal: 16,
  paddingVertical: 6,
  justifyContent: 'center',
  alignItems: 'center',
};
const deleteIconButtonStyle = {
  borderRadius: 8,
  backgroundColor: 'transparent',
  marginLeft: 8,
  height: 32,
  width: 32,
  justifyContent: 'center',
  alignItems: 'center',
};
const inputFieldStyle = {
  width: '100%',
  paddingHorizontal: 10,
  borderRadius: 8,
  backgroundColor: '#fff',
  borderWidth: 1,
  borderColor: '#ccc',
  color: 'black',
  height: 32,
};

interface IdListInputProps {
  label: string;
  infoTitle: string;
  infoUrl: string;
  placeholder: string;
  values: string[];
  setValues: (values: string[]) => void;
  addButtonLabel: string;
  inputValue: string;
  setInputValue: (val: string) => void;
  showInput: boolean;
  setShowInput: (show: boolean) => void;
  Icon: any;
  dispatch: any;
}

const IdListInput: React.FC<IdListInputProps> = ({
  label,
  infoTitle,
  infoUrl,
  placeholder,
  values,
  setValues,
  addButtonLabel,
  inputValue,
  setInputValue,
  showInput,
  setShowInput,
  dispatch,
  Icon,
}) => (
  <>
    <View style={stylesa.inputContainer}>
      {label && <Text style={stylesa.label}>{label}</Text>}
      <TextInput onChangeText={setInputValue} value={inputValue} style={stylesa.input} placeholder={placeholder} />
      <TouchableOpacity
        disabled={inputValue.trim().length <= 0}
        onPress={() => {
          const val = inputValue.trim();
          if (val && !values.includes(val)) {
            setValues([...values, val]);
            setInputValue('');
            setShowInput(false);
          } else {
            dispatch(makeToast({content: 'Value already exists', appearances: 'warning'}));
          }
        }}
        style={stylesa.button}>
        <Text style={stylesa.buttonText}>Add</Text>
      </TouchableOpacity>
    </View>
    <AddedIds values={values} setValues={setValues} />
  </>
  // <div style={styles.inputWrapper}>
  //   {/* <div style={styles.labelStyle}>
  //     {label}
  //     <span style={{marginLeft: 6, verticalAlign: 'middle', cursor: 'pointer'}} title={infoTitle}>
  //       <Icon
  //         style={{marginBottom: 5}}
  //         onPress={() => {
  //           window.open(infoUrl, '_blank');
  //         }}
  //         name="infocirlce"
  //         size={10}
  //         color={theme.TEXT_SECONDARY_COLOR}
  //       />
  //     </span> */}
  //     {/* {!showInput && (
  //       <button style={{...addButtonStyle, marginLeft: '10px'}} onClick={() => setShowInput(true)}>
  //         {addButtonLabel}
  //       </button>
  //     )} */}
  //   {/* </div> */}
  //   {/* {showInput && (
  //     <div style={{display: 'flex', gap: 8, marginBottom: 20, alignItems: 'center', marginTop: 10}}>
  //       <TextInput
  //         onChangeText={setInputValue}
  //         value={inputValue}
  //         style={inputFieldStyle}
  //         placeholder={placeholder}
  //       />
  //       <button
  //         style={addButtonStyle}
  //         onClick={() => {
  //           const val = inputValue.trim();
  //           if (val && !values.includes(val)) {
  //             setValues([...values, val]);
  //             setInputValue('');
  //             setShowInput(false);
  //           } else {
  //             dispatch(makeToast({content: 'Value already exists', appearances: 'warning'}));
  //           }
  //         }}>
  //         Add
  //       </button>
  //     </div>
  //   )} */}
  //   {/* {values.length > 0 && (
  //     <ul style={{paddingLeft: 0, listStyle: 'none'}}>
  //       {values.map((id, idx) => (
  //         <li key={id} style={{display: 'flex', alignItems: 'center', marginBottom: 4}}>
  //           <span style={{color: theme.FOREGROUND, fontSize: 14, marginRight: 8}}>●</span>
  //           <TextElement color="DEFAULT" fontWeight="400" fontSize="md" style={styles.titleStyles}>
  //             {id}
  //           </TextElement>
  //           <button style={deleteIconButtonStyle} onClick={() => setValues(values.filter((_, i) => i !== idx))}>
  //             <Icon name="delete" size={16} color={theme.FOREGROUND} />
  //           </button>
  //         </li>
  //       ))}
  //     </ul>
  //   )} */}
  //          <View style={stylesa.inputContainer}>
  //       <Text style={stylesa.label}>{label}</Text>
  //       <TextInput    onChangeText={setInputValue} value={inputValue}  style={stylesa.input} placeholder={placeholder} />
  //       <TouchableOpacity disabled={inputValue.trim().length<=0}  onPress={() => {
  //           const val = inputValue.trim();
  //           if (val && !values.includes(val)) {
  //             setValues([...values, val]);
  //             setInputValue('');
  //             setShowInput(false);
  //           } else {
  //             dispatch(makeToast({content: 'Value already exists', appearances: 'warning'}));
  //           }
  //         }} style={stylesa.button}>
  //         <Text style={stylesa.buttonText}>Add</Text>
  //       </TouchableOpacity>
  //     </View>
  //     <AddedIds values={values} setValues={setValues} />
  // </div>
);

const InAppPurchaseDetail = ({
  integration,
  onClose,
  setLoading,
  isIntegrated,
  subscriptionIdList,
  setSubscriptionIdList,
  productIdList,
  setProductIdList,
  consumableProductIdList,
  setConsumableProductIdList,
  isModal,
  handleDisconnectIntegraion
}: any) => {
  const params = useParams<{id?: string}>();
  const appId = params?.id || '';
  const dispatch = useDispatch();
  const [showSubscriptionInput, setShowSubscriptionInput] = useState(false);
  const [showProductInput, setShowProductInput] = useState(false);
  const [showConsumableInput, setShowConsumableInput] = useState(false);
  const [subscriptionInput, setSubscriptionInput] = useState('');
  const [productInput, setProductInput] = useState('');
  const [consumableInput, setConsumableInput] = useState('');
  const [loading, setLocalLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('SUBSCRIBE');
  const [integrated, setIntegrated] = useState(isIntegrated);

  const handleSaveOrUpdate = async () => {
    setLocalLoading(true);
    setLoading(true);
    console.log(integrated, isIntegrated, 'ams');
    try {
      if (integrated) {
        const updatePayload = {
          subscriptionIds: subscriptionIdList.join(','),
          productIds: productIdList.join(','),
          consumableProductIds: consumableProductIdList.join(','),
        };
        await IntegrationsApi.saveAppIntegrationCredentials(appId, PLATFORM_TYPE, updatePayload, true);
        dispatch(makeToast({content: 'In-App purchase Integration updated', appearances: 'success'}));
      } else {
        if (subscriptionIdList?.length > 0 || productIdList?.length > 0 || consumableProductIdList?.length > 0) {
          const createPayload = {
            platformType: PLATFORM_TYPE,
            credentials: {
              subscriptionIds: subscriptionIdList.join(','),
              productIds: productIdList.join(','),
              consumableProductIds: consumableProductIdList.join(','),
            },
          };
          await IntegrationsApi.createIntegration(appId, createPayload);
          const updatePayload = {
            subscriptionIds: subscriptionIdList.join(','),
            productIds: productIdList.join(','),
            consumableProductIds: consumableProductIdList.join(','),
          };
          await IntegrationsApi.saveAppIntegrationCredentials(appId, PLATFORM_TYPE, updatePayload, true);
          setIntegrated(true);
          dispatch(makeToast({content: 'In-App purchase Integration created', appearances: 'success'}));
        } else {
          dispatch(
            makeToast({
              content: 'Please add atleast one Subscription Id, Product Id or Consumable Product Id',
              appearances: 'warning',
            }),
          );
        }
      }
      store.dispatch(
        modelUpdateAction([
          {selector: ['AppMarketplaceIap', 'productIds'], newValue: productIdList.join(',')},
          {selector: ['AppMarketplaceIap', 'subscriptionIds'], newValue: subscriptionIdList.join(',')},
          {selector: ['AppMarketplaceIap', 'consumableProductIds'], newValue: consumableProductIdList.join(',')},
        ]),
      );
    } finally {
      setLocalLoading(false);
      setLoading(false);
      if(subscriptionIdList?.length <= 0 && productIdList?.length <= 0 && consumableProductIdList?.length <= 0){
        handleDisconnectIntegraion();
      }
      Analytics.track('agent_config_saved', {
        integration: integration.title,
      });
      isModal && onClose();
    }
  };

  useEffect(() => {
    Analytics.track('agent_integration_viewed', {
      integration: integration.title,
    });
  }, []);

  return isModal ? (
    <View style={stylesa.container}>
      <View style={stylesx.header}>
        <Text style={stylesx.title}>Configuration Fields</Text>
        <TouchableOpacity onPress={() => onClose()} style={stylesx.closeButton}>
          <Text style={stylesx.closeButtonText}>×</Text>
        </TouchableOpacity>
      </View>

      <IdListInput
        label="Subscription ID"
        infoTitle="More info about Subscription Ids"
        infoUrl="https://www.tile.dev/"
        placeholder="Enter Subscription ID"
        values={subscriptionIdList}
        setValues={setSubscriptionIdList}
        addButtonLabel="Add"
        inputValue={subscriptionInput}
        setInputValue={setSubscriptionInput}
        showInput={showSubscriptionInput}
        setShowInput={setShowSubscriptionInput}
        Icon={Icon}
        dispatch={dispatch}
      />
      <IdListInput
        label="Product ID"
        infoTitle="More info about Product Ids"
        infoUrl="https://www.tile.dev/"
        placeholder="Enter Product ID"
        values={productIdList}
        setValues={setProductIdList}
        addButtonLabel="Add"
        inputValue={productInput}
        setInputValue={setProductInput}
        showInput={showProductInput}
        setShowInput={setShowProductInput}
        dispatch={dispatch}
        Icon={Icon}
      />
      <IdListInput
        label="Consumable Product ID"
        infoTitle="More info about Consumable Product Ids"
        infoUrl="https://www.tile.dev/"
        placeholder="Enter Consumable Product ID"
        values={consumableProductIdList}
        setValues={setConsumableProductIdList}
        addButtonLabel="Add"
        inputValue={consumableInput}
        setInputValue={setConsumableInput}
        showInput={showConsumableInput}
        setShowInput={setShowConsumableInput}
        Icon={Icon}
        dispatch={dispatch}
      />
      <TouchableOpacity onPress={handleSaveOrUpdate} style={stylesa.saveButton}>
        <Text style={stylesa.saveButtonText}>{loading ? 'Saving...' : 'Save'}</Text>
      </TouchableOpacity>
    </View>
  ) : (
    <View style={stylesa.container}>
      <View style={stylesx.header}>
        <Text style={stylesx.title}>Configuration Fields</Text>
        <TouchableOpacity onPress={handleSaveOrUpdate} style={[stylesa.saveButton, {marginTop: 0}]}>
          <Text style={stylesa.saveButtonText}>{loading ? 'Saving...' : 'Save'}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.tabsSection}>
        <View style={styles.tabs}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'SUBSCRIBE' && styles.active]}
            onPress={() => {
              setActiveTab('SUBSCRIBE');
            }}>
            <Text style={styles.tabText}>Subscription ID</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'PRODUCT' && styles.active]}
            onPress={() => {
              setActiveTab('PRODUCT');
            }}>
            <Text style={styles.tabText}>Product ID</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'CONSUMER' && styles.active]}
            onPress={() => {
              setActiveTab('CONSUMER');
            }}>
            <Text style={styles.tabText}>Consumable Product ID</Text>
          </TouchableOpacity>
        </View>
      </View>
      {activeTab === 'SUBSCRIBE' && (
        <IdListInput
          label=""
          infoTitle="More info about Subscription Ids"
          infoUrl="https://www.tile.dev/"
          placeholder="Enter Subscription ID"
          values={subscriptionIdList}
          setValues={setSubscriptionIdList}
          addButtonLabel="Add"
          inputValue={subscriptionInput}
          setInputValue={setSubscriptionInput}
          showInput={showSubscriptionInput}
          setShowInput={setShowSubscriptionInput}
          Icon={Icon}
          dispatch={dispatch}
        />
      )}
      {activeTab == 'PRODUCT' && (
        <IdListInput
          label=""
          infoTitle="More info about Product Ids"
          infoUrl="https://www.tile.dev/"
          placeholder="Enter Product ID"
          values={productIdList}
          setValues={setProductIdList}
          addButtonLabel="Add"
          inputValue={productInput}
          setInputValue={setProductInput}
          showInput={showProductInput}
          setShowInput={setShowProductInput}
          dispatch={dispatch}
          Icon={Icon}
        />
      )}
      {activeTab === 'CONSUMER' && (
        <IdListInput
          label=""
          infoTitle="More info about Consumable Product Ids"
          infoUrl="https://www.tile.dev/"
          placeholder="Enter Consumable Product ID"
          values={consumableProductIdList}
          setValues={setConsumableProductIdList}
          addButtonLabel="Add"
          inputValue={consumableInput}
          setInputValue={setConsumableInput}
          showInput={showConsumableInput}
          setShowInput={setShowConsumableInput}
          Icon={Icon}
          dispatch={dispatch}
        />
      )}
    </View>
  );
};

export default InAppPurchaseDetail;

const styles = StyleSheet.create({
  tabsSection: {
    display: 'flex',
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: 'clamp(4px, 2vw, 10px)',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#121212',
    marginTop: 12,
    borderRadius: 12, // Use a number, not a string or clamp
  },
  tab: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 12, // Use a number, not clamp
    paddingHorizontal: 24, // Use a number, not clamp
    borderRadius: 12, // Use a number, not clamp
    fontSize: 16, // Use a number, not clamp
    fontWeight: '500',
  },
  active: {
    backgroundColor: '#34363E',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  tabText: {
    color: '#EFEFEF',
    fontSize: 14, // Use a number, not clamp
    fontWeight: '500',
    letterSpacing: 0.32,
  },
  container: {
    minHeight: 400,
  },
  header: {
    color: '#fff',
    fontSize: 21,
    marginBottom: 12,
  },
  tagline: {
    color: '#fff',
    marginBottom: 24,
    marginTop: 30,
  },
  inputWrapper: {
    marginBottom: 8,
    marginTop: 8,
  },
  inputStyle: {
    width: '100%',
    padding: 10,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    color: 'black',
    marginBottom: 16,
    marginTop: 2,
  },
  labelStyle: {
    color: '#fff',
    alignItems: 'center',
    marginBottom: 5,
    marginTop: 10,
  },
  logoWrapper: {
    width: 50,
    overflow: 'hidden',
    marginRight: 12,
  },
  flexDirectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  titleStyles: {color: '#fff'},
});


const AddedIds = ({values, setValues}) => {
  if (values.length <= 0) return null;
  return (
    <View style={stylesf.container}>
      <Text style={stylesf.title}>Added IDs</Text>

      <View>
        {values.map((id, idx) => (
          <View style={stylesf.itemContainer}>
            <Text style={stylesf.itemText}>{id}</Text>
            <View style={stylesf.iconContainer}>
              <TouchableOpacity
                onPress={() => setValues(values.filter((_, i) => i !== idx))}
                style={stylesf.iconButton}>
                {' '}
                <Icon name="delete" size={14} color={theme.FOREGROUND} />
              </TouchableOpacity>
              <TouchableOpacity style={stylesf.iconButton}>{/* Here, place your copy icon */}</TouchableOpacity>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const stylesa = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: '#1A1D20',
    padding: 24,
    borderRadius: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    color: '#ffffff',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  label: {
    color: '#EFEFEF',
    flex: 1,
  },
  input: {
    flex: 2,
    height: 40,
    borderColor: '#3D3D3D',
    borderWidth: 1,
    borderRadius: 10,
    backgroundColor: '#22262A',
    color: '#565656',
    paddingHorizontal: 10,
    fontSize: 14,
  },
  button: {
    backgroundColor: 'transparent',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'white',
    padding: 10,
    paddingHorizontal: 16,
    marginLeft: 14,
  },
  buttonText: {
    color: '#ffffff',
  },
  saveButton: {
    backgroundColor: '#007bff',
    borderRadius: 8,
    alignSelf: 'flex-end',
    width: 'clamp(90px, 6vw, 200px)',
    padding: 8,
    marginTop: 12,
  },
  saveButtonText: {
    color: '#ffffff',
    textAlign: 'center',
    fontSize: 16,
  },
});

const stylesf = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#121212',
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 12,
    color: '#FFFFFF',
    marginBottom: 12,
  },
  itemContainer: {
    backgroundColor: '#1A1D20',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemText: {
    color: '#B9B9B9',
    fontSize: 14,
  },
  iconContainer: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: 8,
  },
});

const stylesx = StyleSheet.create({
  modal: {
    backgroundColor: '#1C1C1E',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    alignSelf: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 20,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 20,
  },
  subtitle: {
    fontSize: 12,
    color: '#9A9A9A',
    marginBottom: 16,
  },
  projectsContainer: {
    marginBottom: 16,
  },
  projectCard: {
    backgroundColor: '#161618',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#3D3D3D',
  },
  connectedCard: {
    backgroundColor: '#161618',
  },
  selectedCard: {
    borderWidth: 0,
    backgroundColor: '#34363E',
  },
  projectLeft: {
    flex: 1,
  },
  projectName: {
    color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 14,
    marginBottom: 4,
  },
  projectRegion: {
    fontSize: 12,
    color: '#9A9A9A',
  },
  regionText: {
    color: '#B0B0B0',
  },
  projectRight: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  connectedText: {
    color: '#3AB44A',
    fontWeight: '700',
    fontSize: 12,
    marginBottom: 6,
  },
  projectStatus: {
    fontSize: 12,
    color: '#9A9A9A',
  },
  statusText: {
    fontWeight: '700',
    color: '#B0B0B0',
  },
  createNewButton: {
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#6E6E7E',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  createNewButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  connectButton: {
    height: 40,
    backgroundColor: '#2563EB',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    paddingHorizontal: 24,
  },
  disonnectButton: {
    height: 40,
    backgroundColor: '#EB3223',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    paddingHorizontal: 24,
  },
  connectButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
});
